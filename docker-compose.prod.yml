version: '3.8'

services:
  # Core API Service
  api:
    build:
      context: ./
      dockerfile: ./docker/api.prod.Dockerfile
    ports:
      - '3000:3000'
    env_file:
      - ./backend/.env.docker
    environment:
      - NODE_ENV=production
    depends_on:
      - pgsql
      - redis
      - kafka
      - rabbitmq
    networks:
      - tukxi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Kafka Consumers Microservice
  kafka-consumers:
    build:
      context: ./
      dockerfile: ./docker/kafka-consumers.prod.Dockerfile
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=pgsql
      - REDIS_HOST=redis
      - KAFKA_BROKERS=kafka:9092
      - KAFKA_CONSUMER_PORT=3001
    depends_on:
      - pgsql
      - redis
      - kafka
    networks:
      - tukxi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # RabbitMQ Consumer Microservice
  rabbitmq-consumer:
    build:
      context: ./
      dockerfile: ./docker/rabbitmq-consumer.prod.Dockerfile
    ports:
      - '3003:3003'
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=pgsql
      - REDIS_HOST=redis
      - RABBITMQ_URL=amqp://tukxi:password@rabbitmq:5672
      - RABBITMQ_CONSUMER_PORT=3003
    depends_on:
      - pgsql
      - redis
      - rabbitmq
    networks:
      - tukxi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Infrastructure Services
  
  # PostgreSQL Database
  pgsql:
    image: 'postgres:15-alpine'
    restart: always
    environment:
      POSTGRES_DB: 'tukxi'
      POSTGRES_USER: 'tukxi_db_user'
      POSTGRES_PASSWORD: 'password'
    volumes:
      - pgsql_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    networks:
      - tukxi-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tukxi_db_user -d tukxi"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: 'redis:7-alpine'
    restart: always
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - tukxi-network
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka Message Broker (KRaft mode - no ZooKeeper needed)
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    restart: always
    ports:
      - '9092:9092'
      - '29092:29092'
    environment:
      # KRaft mode configuration
      KAFKA_NODE_ID: 1
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@kafka:9093'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
      KAFKA_LISTENERS: 'PLAINTEXT://kafka:9092,CONTROLLER://kafka:9093,PLAINTEXT_HOST://0.0.0.0:29092'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
      KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092'

      # Cluster configuration
      CLUSTER_ID: 'MkU3OEVBNTcwNTJENDM2Qk'

      # Topic and replication settings
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'

      # Log settings
      KAFKA_LOG_DIRS: '/tmp/kraft-combined-logs'
    volumes:
      - kafka_data:/tmp/kraft-combined-logs
    networks:
      - tukxi-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RabbitMQ Message Broker
  rabbitmq:
    image: 'rabbitmq:3.12-management-alpine'
    restart: always
    ports:
      - '5672:5672'   # AMQP port
      - '15672:15672' # Management UI port
    environment:
      RABBITMQ_DEFAULT_USER: 'tukxi'
      RABBITMQ_DEFAULT_PASS: 'password'
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tukxi-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

# Named volumes for data persistence
volumes:
  pgsql_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  rabbitmq_data:
    driver: local

# Custom network for service communication
networks:
  tukxi-network:
    driver: bridge
