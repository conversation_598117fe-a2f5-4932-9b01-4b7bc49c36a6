import { IsString, IsN<PERSON>ber, IsOptional, IsEnum } from 'class-validator';

export enum DriverStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  BUSY = 'busy',
  IN_RIDE = 'in_ride',
}

export class DriverLocationReceivedDto {
  @IsString()
  driverId!: string;

  @IsNumber()
  lat!: number;

  @IsNumber()
  lon!: number;

  @IsString()
  timestamp!: string;

  @IsString()
  city!: string;

  @IsEnum(DriverStatus)
  status!: DriverStatus;

  @IsOptional()
  @IsString()
  rideId?: string;
}
