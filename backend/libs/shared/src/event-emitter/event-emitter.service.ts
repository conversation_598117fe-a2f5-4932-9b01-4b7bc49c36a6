import { Injectable, Logger } from '@nestjs/common';
import { IEventName } from './interface/events.enum';
import { KafkaProducerService } from '../kafka';

@Injectable()
export class GlobalEventEmitterService {
  private readonly logger = new Logger(GlobalEventEmitterService.name);

  constructor(private readonly kafkaProducerService: KafkaProducerService) {}

  async emit(eventName: IEventName, data: any): Promise<void> {
    try {
      this.logger.log(`Emitting event: ${eventName}`);
      //emit to Kafka
      await this.kafkaProducerService.emitEvent(eventName, data);

      // emit rabitmq event if needed
      // this.rabbitMqProducerService.emitEvent(eventName, data);
    } catch (error) {
      this.logger.error(`Failed to emit event: ${eventName}`, error);
    }
  }

  // Example usage:
  // await this.eventEmitter.emit(IEventName.DRIVER_CREATED, {
  //   driverId: '123',
  //   name: '',
  // });
}
