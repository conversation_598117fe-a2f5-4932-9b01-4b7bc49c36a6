import {
  Inject,
  Injectable,
  Logger,
  OnM<PERSON>uleD<PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { IEventName } from '../event-emitter/interface/events.enum';

@Injectable()
export class KafkaProducerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaProducerService.name);

  constructor(
    @Inject('KAFKA_SERVICE') private readonly kafkaClient: ClientKafka,
  ) {}

  async onModuleInit() {
    await this.kafkaClient.connect();
  }

  async onModuleDestroy() {
    await this.kafkaClient.close();
  }

  async emitEvent(eventName: IEventName, data: any): Promise<void> {
    const message = {
      eventName,
      data,
      timestamp: new Date().toISOString(),
      source: 'tukxi-api',
    };

    this.logger.log(`[KAFKA PRODUCER] Emitting event: ${eventName}`);
    this.kafkaClient.emit(eventName, {
      key: eventName,
      value: JSON.stringify(message),
    });
  }
}
