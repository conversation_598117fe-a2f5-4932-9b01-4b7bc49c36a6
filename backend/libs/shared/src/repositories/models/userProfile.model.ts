import { BaseEntity } from '../base.repository';
import { Role } from './role.model';

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

export enum UserProfileStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  DISABLED = 'disabled',
  INACTIVE = 'inactive',
}

export interface UserProfile extends BaseEntity {
  userId: string;
  roleId: string;
  firstName: string;
  lastName: string;
  cityId?: string;
  languageId?: string;
  referralCode: string;
  profilePictureUrl?: string;
  gender?: Gender;
  status: UserProfileStatus;
  dob?: Date;
  role?: Role;
  // Optionally add city and user relations if needed
}
