import { BaseEntity } from '../base.repository';

export interface LanguageSpec {
  [key: string]:
    | {
        [languageCode: string]: string;
      }
    | undefined;
}

export interface Product extends BaseEntity {
  vehicleTypeId: string;
  productServiceId?: string | null;
  name: string;
  description?: string | null;
  identifier?: string | null;
  icon?: string | null;
  languageSpec?: LanguageSpec | null;
  isEnabled: boolean;

  // Relations
  vehicleType?: any; // Replace 'any' with VehicleType if available
  productService?: any; // Replace 'any' with ProductService if available
  cityProducts?: any[]; // Replace 'any' with CityProduct[] if available
}
