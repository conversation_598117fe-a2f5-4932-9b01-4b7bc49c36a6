import { BaseEntity } from '../base.repository';

export enum CityStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface City extends BaseEntity {
  name: string;
  icon?: string | null;
  state?: string | null;
  country?: string | null;
  polygon?: any | null; // GeoJSON polygon
  h3Indexes: string[];
  status: CityStatus;

  // Relations
  userProfiles?: any[];
  cityProducts?: any[];
}
