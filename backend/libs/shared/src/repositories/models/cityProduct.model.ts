import { BaseEntity } from '../base.repository';
import { City } from './city.model';
import { DriverVehicle } from './driverVehicle.model';
import { Product } from './product.model';
import { VehicleType } from './vehicleType.model';

export interface CityProduct extends BaseEntity {
  cityId: string;
  productId: string;
  vehicleTypeId: string;
  isEnabled: boolean;

  // Relations
  city?: City;
  product?: Product;
  vehicleType?: VehicleType;
  driverVehicles?: DriverVehicle[];
}
