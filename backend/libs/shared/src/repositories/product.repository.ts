import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { Product } from './models/product.model';

@Injectable()
export class ProductRepository extends BaseRepository<Product> {
  protected readonly modelName = 'product';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new product record.
   * @param data - Product data excluding id and timestamps
   */
  async createProduct(
    data: Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Product> {
    return this.create(data, {
      include: {
        vehicleType: true,
        productService: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Find all products.
   */
  async findAllProducts(): Promise<Product[]> {
    return this.findMany({
      include: {
        vehicleType: true,
        productService: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Find product by ID.
   * @param id - Product ID
   */
  async findProductById(id: string): Promise<Product | null> {
    return this.findById(id, {
      include: {
        vehicleType: true,
        productService: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Find product by identifier.
   * @param identifier - Product identifier
   */
  async findProductByIdentifier(identifier: string): Promise<Product | null> {
    return this.findOne({
      where: { identifier },
      include: {
        vehicleType: true,
        productService: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Update product by ID.
   * @param id - Product ID
   * @param data - Partial product data
   */
  async updateProduct(id: string, data: Partial<Product>): Promise<Product> {
    return this.updateById(id, data, {
      include: {
        vehicleType: true,
        productService: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Soft delete product by ID.
   * @param id - Product ID
   */
  async deleteProduct(id: string): Promise<Product> {
    return this.softDeleteById(id);
  }

  /**
   * Get paginated products.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateProducts(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, {
      include: {
        vehicleType: true,
        cityProducts: true,
      },
      ...options,
    });
  }

  /**
   * Find products by vehicle type ID.
   * @param vehicleTypeId - Vehicle type ID
   */
  async findProductsByVehicleTypeId(vehicleTypeId: string): Promise<Product[]> {
    return this.findMany({
      where: {
        vehicleTypeId,
      },
      include: {
        vehicleType: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Find products by city ID through cityProducts relation.
   * @param cityId - City ID
   */
  async findProductsByCityId(cityId: string): Promise<Product[]> {
    return this.findMany({
      where: {
        cityProducts: {
          some: {
            cityId,
            isEnabled: true,
          },
        },
      },
      include: {
        vehicleType: true,
        cityProducts: {
          where: {
            cityId,
          },
        },
      },
    });
  }

  /**
   * Find active products by city and vehicle type.
   * @param cityId - City ID
   * @param vehicleTypeId - Vehicle type ID
   */
  async findActiveProductsByCityAndVehicleType(
    cityId: string,
    vehicleTypeId: string,
  ): Promise<Product[]> {
    return this.findMany({
      where: {
        vehicleTypeId,
        cityProducts: {
          some: {
            cityId,
            isEnabled: true,
          },
        },
      },
      include: {
        vehicleType: true,
        productService: true,
        cityProducts: {
          where: {
            cityId,
          },
        },
      },
    });
  }
}
