import { Injectable } from '@nestjs/common';
import { RedisService } from '../database/redis/redis.service';
import { BaseRedisRepository } from './base-redis.repository';

/**
 * Repository for managing drivers within H3 cells using Redis sorted sets.
 * Key format: drivers:cell:{city}:{h3Index}
 * Score: timestamp (for latest location updates)
 * Members: driverId
 */
@Injectable()
export class CellDriverRepository extends BaseRedisRepository {
  constructor(redisService: RedisService) {
    super(redisService);
  }

  /**
   * Generate key for drivers in a specific H3 cell
   * Format: drivers:cell:{city}:{h3Index}
   */
  private generateCellKey(city: string, h3Index: string): string {
    return this.generateKey(['drivers', 'cell', city, h3Index]);
  }

  /**
   * Add driver to H3 cell with timestamp as score
   */
  async addDriver(
    city: string,
    h3Index: string,
    driverId: string,
    timestamp: number = Date.now(),
  ): Promise<boolean> {
    try {
      const key = this.generateCellKey(city, h3Index);
      const result = await this.client.zadd(key, timestamp, driverId);

      this.logger.log(
        `Added driver ${driverId} to cell ${h3Index} in city ${city}`,
      );
      return result === 1; // Returns 1 if new member was added, 0 if updated
    } catch (error) {
      this.handleRedisError(
        `addDriver (${city}, ${h3Index}, ${driverId})`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove driver from H3 cell
   */
  async removeDriver(
    city: string,
    h3Index: string,
    driverId: string,
  ): Promise<boolean> {
    try {
      const key = this.generateCellKey(city, h3Index);
      const result = await this.client.zrem(key, driverId);

      this.logger.log(
        `Removed driver ${driverId} from cell ${h3Index} in city ${city}`,
      );
      return result === 1; // Returns 1 if member was removed, 0 if not found
    } catch (error) {
      this.handleRedisError(
        `removeDriver (${city}, ${h3Index}, ${driverId})`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all drivers in a specific H3 cell
   * @param withScores - If true, returns drivers with their timestamps
   */
  async findDriverIds(
    city: string,
    h3Index: string,
    withScores: boolean = false,
  ): Promise<string[] | Array<{ driverId: string; timestamp: number }>> {
    try {
      const key = this.generateCellKey(city, h3Index);

      if (withScores) {
        const results = await this.client.zrange(key, 0, -1, 'WITHSCORES');
        const drivers: Array<{ driverId: string; timestamp: number }> = [];

        for (let i = 0; i < results.length; i += 2) {
          drivers.push({
            driverId: results[i],
            timestamp: parseFloat(results[i + 1]),
          });
        }

        return drivers;
      } else {
        return await this.client.zrange(key, 0, -1);
      }
    } catch (error) {
      this.handleRedisError(`findDriverIds (${city}, ${h3Index})`, error);
      throw error;
    }
  }

  /**
   * Get drivers in a cell ordered by recency (most recent first)
   */
  async findRecentDriverIds(
    city: string,
    h3Index: string,
    limit: number = 50,
  ): Promise<Array<{ driverId: string; timestamp: number }>> {
    try {
      const key = this.generateCellKey(city, h3Index);
      const results = await this.client.zrevrange(
        key,
        0,
        limit - 1,
        'WITHSCORES',
      );
      const drivers: Array<{ driverId: string; timestamp: number }> = [];

      for (let i = 0; i < results.length; i += 2) {
        drivers.push({
          driverId: results[i],
          timestamp: parseFloat(results[i + 1]),
        });
      }

      return drivers;
    } catch (error) {
      this.handleRedisError(`findRecentDriverIds (${city}, ${h3Index})`, error);
      throw error;
    }
  }

  /**
   * Check if driver exists in a specific cell
   */
  async hasDriver(
    city: string,
    h3Index: string,
    driverId: string,
  ): Promise<boolean> {
    try {
      const key = this.generateCellKey(city, h3Index);
      const score = await this.client.zscore(key, driverId);
      return score !== null;
    } catch (error) {
      this.handleRedisError(
        `hasDriver (${city}, ${h3Index}, ${driverId})`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get count of drivers in a specific cell
   */
  async getDriverCount(city: string, h3Index: string): Promise<number> {
    try {
      const key = this.generateCellKey(city, h3Index);
      return await this.client.zcard(key);
    } catch (error) {
      this.handleRedisError(`getDriverCount (${city}, ${h3Index})`, error);
      throw error;
    }
  }

  /**
   * Remove old entries from a cell (cleanup)
   * @param maxAge - Maximum age in milliseconds (default: 24 hours)
   */
  async cleanupOldDrivers(
    city: string,
    h3Index: string,
    maxAge: number = 24 * 60 * 60 * 1000,
  ): Promise<number> {
    try {
      const key = this.generateCellKey(city, h3Index);
      const cutoffTimestamp = Date.now() - maxAge;
      const removed = await this.client.zremrangebyscore(
        key,
        0,
        cutoffTimestamp,
      );

      if (removed > 0) {
        this.logger.log(
          `Cleaned up ${removed} old drivers from cell ${h3Index} in city ${city}`,
        );
      }

      return removed;
    } catch (error) {
      this.handleRedisError(`cleanupOldDrivers (${city}, ${h3Index})`, error);
      throw error;
    }
  }

  /**
   * Move driver from one cell to another atomically
   */
  async moveDriver(
    city: string,
    fromH3Index: string,
    toH3Index: string,
    driverId: string,
    timestamp: number = Date.now(),
  ): Promise<boolean> {
    try {
      const fromKey = this.generateCellKey(city, fromH3Index);
      const toKey = this.generateCellKey(city, toH3Index);

      // Use pipeline for atomic operation
      const pipeline = this.client.pipeline();
      pipeline.zrem(fromKey, driverId);
      pipeline.zadd(toKey, timestamp, driverId);

      const results = await pipeline.exec();

      this.logger.log(
        `Moved driver ${driverId} from cell ${fromH3Index} to ${toH3Index} in city ${city}`,
      );

      return results !== null && results.every(([err]) => !err);
    } catch (error) {
      this.handleRedisError(
        `moveDriver (${city}, ${fromH3Index} -> ${toH3Index}, ${driverId})`,
        error,
      );
      throw error;
    }
  }
}
