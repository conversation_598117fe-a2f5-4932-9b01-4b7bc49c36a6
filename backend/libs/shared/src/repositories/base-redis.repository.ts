import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../database/redis/redis.service';
import Redis from 'ioredis';

@Injectable()
export abstract class BaseRedisRepository {
  protected readonly logger = new Logger(this.constructor.name);
  protected readonly client: Redis;

  constructor(protected readonly redisService: RedisService) {
    this.client = this.redisService.getClient();
  }

  /**
   * Generate Redis key with consistent naming convention
   */
  protected generateKey(parts: string[]): string {
    return parts.filter(Boolean).join(':');
  }

  /**
   * Safely parse JSON string
   */
  protected safeJsonParse<T>(jsonString: string | null): T | null {
    if (!jsonString) return null;
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      this.logger.error(`Failed to parse JSON: ${jsonString}`, error);
      return null;
    }
  }

  /**
   * Safely stringify JSON
   */
  protected safeJsonStringify(data: any): string {
    try {
      return JSON.stringify(data);
    } catch (error) {
      this.logger.error('Failed to stringify JSON', error);
      throw error;
    }
  }

  /**
   * Handle Redis errors with logging
   */
  protected handleRedisError(operation: string, error: any): void {
    this.logger.error(
      `Redis ${operation} error: ${error.message}`,
      error.stack,
    );
    throw error;
  }
}
