import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { ProductService } from './models/productService.model';

@Injectable()
export class ProductServiceRepository extends BaseRepository<ProductService> {
  protected readonly modelName = 'productService';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createProductService(
    data: Omit<ProductService, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<ProductService> {
    return this.create(data);
  }

  async findAllProductServices(): Promise<ProductService[]> {
    return this.findMany();
  }

  async findProductServiceById(id: string): Promise<ProductService | null> {
    return this.findById(id);
  }

  async findProductServiceByIdentifier(
    identifier: string,
  ): Promise<ProductService | null> {
    return this.findOne({
      where: { identifier },
    });
  }

  async updateProductService(
    id: string,
    data: Partial<ProductService>,
  ): Promise<ProductService> {
    return this.updateById(id, data);
  }

  async deleteProductService(id: string): Promise<ProductService> {
    return this.softDeleteById(id);
  }

  async permanentDeleteProductService(id: string): Promise<ProductService> {
    return this.hardDeleteById(id);
  }
}
