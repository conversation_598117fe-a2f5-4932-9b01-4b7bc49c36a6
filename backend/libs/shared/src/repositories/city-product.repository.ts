import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { CityProduct } from './models/cityProduct.model';

@Injectable()
export class CityProductRepository extends BaseRepository<CityProduct> {
  protected readonly modelName = 'cityProduct';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new city product record.
   * @param data - CityProduct data excluding id and timestamps
   */
  async createCityProduct(
    data: Omit<CityProduct, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<CityProduct> {
    return this.create(data, {
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }

  /**
   * Find all city products.
   */
  async findAllCityProducts(): Promise<CityProduct[]> {
    return this.findMany({
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }

  /**
   * Find city product by ID.
   * @param id - CityProduct ID
   */
  async findCityProductById(id: string): Promise<CityProduct | null> {
    return this.findById(id, {
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }

  /**
   * Update city product by ID.
   * @param id - CityProduct ID
   * @param data - Partial CityProduct data
   */
  async updateCityProduct(
    id: string,
    data: Partial<CityProduct>,
  ): Promise<CityProduct> {
    return this.updateById(id, data, {
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }

  /**
   * Delete city product by ID (soft delete).
   * @param id - CityProduct ID
   */
  async deleteCityProduct(id: string): Promise<CityProduct> {
    return this.softDeleteById(id);
  }

  /**
   * Find city product by city ID, product ID, and vehicle type ID.
   * @param cityId - City ID
   * @param productId - Product ID
   * @param vehicleTypeId - Vehicle type ID
   */
  async findCityProductByCityProductVehicle(
    cityId: string,
    productId: string,
    vehicleTypeId: string,
  ): Promise<CityProduct | null> {
    return this.findOne({
      where: {
        cityId,
        productId,
        vehicleTypeId,
      },
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }

  /**
   * Find all city products by city ID.
   * @param cityId - City ID
   */
  async findCityProductsByCityId(cityId: string): Promise<CityProduct[]> {
    return this.findMany({
      where: {
        cityId,
      },
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }

  /**
   * Paginate city products by city ID with filters.
   * @param cityId - City ID
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateCityProductsByCityId(
    cityId: string,
    page = 1,
    limit = 10,
    options?: any,
  ) {
    return this.paginate(page, limit, {
      where: {
        cityId,
        ...options?.where,
      },
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
      ...options,
    });
  }

  /**
   * Update all city products status by city ID.
   * @param cityId - City ID
   * @param isEnabled - Enable/disable status
   */
  async updateAllCityProductsStatusByCityId(
    cityId: string,
    isEnabled: boolean,
  ): Promise<void> {
    await this.prisma.cityProduct.updateMany({
      where: { cityId },
      data: { isEnabled },
    });
  }

  /**
   * Delete multiple city products by IDs.
   * @param ids - Array of CityProduct IDs
   */
  async deleteCityProductsByIds(ids: string[]): Promise<void> {
    await this.prisma.cityProduct.updateMany({
      where: {
        id: {
          in: ids,
        },
      },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * Find city products by product IDs and city ID.
   * @param cityId - City ID
   * @param productIds - Array of Product IDs
   */
  async findCityProductsByProductIds(
    cityId: string,
    productIds: string[],
  ): Promise<CityProduct[]> {
    return this.findMany({
      where: {
        cityId,
        productId: {
          in: productIds,
        },
      },
      include: {
        city: true,
        product: true,
        vehicleType: true,
        driverVehicles: true,
      },
    });
  }
}
