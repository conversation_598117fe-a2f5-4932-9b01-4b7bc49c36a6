import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  DriverVehicleDocument,
  DriverVehicleDocumentStatus,
} from './models/driverVehicleDocument.model';

@Injectable()
export class DriverVehicleDocumentRepository extends BaseRepository<DriverVehicleDocument> {
  protected readonly modelName = 'driverVehicleDocument';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Find driver vehicle documents by driver vehicle ID
   * @param driverVehicleId Driver vehicle ID
   */
  async findByDriverVehicleId(
    driverVehicleId: string,
  ): Promise<DriverVehicleDocument[]> {
    return this.findMany({
      where: { driverVehicleId },
      include: {
        vehicleDocument: {
          select: { id: true, name: true, description: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Update document status
   * @param id Document ID
   * @param status New status
   * @param rejectionNote Optional rejection note
   */
  async updateStatus(
    id: string,
    status: DriverVehicleDocumentStatus,
    rejectionNote?: string,
  ): Promise<DriverVehicleDocument> {
    const updateData: any = { status };
    if (status === DriverVehicleDocumentStatus.REJECTED && rejectionNote) {
      updateData.rejectionNote = rejectionNote;
    } else if (status === DriverVehicleDocumentStatus.APPROVED) {
      updateData.rejectionNote = null;
    }

    return this.updateById(id, updateData);
  }

  /**
   * Check if all mandatory documents for a vehicle are approved
   * @param driverVehicleId Driver vehicle ID
   */
  async areAllMandatoryDocumentsApproved(
    driverVehicleId: string,
  ): Promise<boolean> {
    // Get all vehicle documents (for now, consider all as mandatory)
    const vehicleDocuments = await this.prisma.vehicleDocument.findMany({
      select: { id: true },
    });

    if (vehicleDocuments.length === 0) {
      return true; // No documents required
    }

    // Check if all documents are approved for this vehicle
    const approvedCount = await this.prisma.driverVehicleDocument.count({
      where: {
        driverVehicleId,
        vehicleDocumentId: {
          in: vehicleDocuments.map((doc) => doc.id),
        },
        status: 'APPROVED',
      },
    });

    return approvedCount === vehicleDocuments.length;
  }

  async getAllApprovedDriverDocument(driverVehicleId: string): Promise<any[]> {
    // Check if all documents are approved for this vehicle
    return await this.prisma.driverVehicleDocument.findMany({
      where: {
        driverVehicleId,
        status: DriverVehicleDocumentStatus.APPROVED,
      },
    });
  }
}
