import { Injectable } from '@nestjs/common';
import { RedisService } from '../database/redis/redis.service';
import { BaseRedisRepository } from './base-redis.repository';

/**
 * Repository for managing driver to H3 cell mappings using Redis hash.
 * Key format: driver:locations
 * Hash fields: driverId -> h3Index
 */
@Injectable()
export class DriverCellMappingRepository extends BaseRedisRepository {
  private readonly DRIVER_LOCATIONS_KEY = 'driver:locations';

  constructor(redisService: RedisService) {
    super(redisService);
  }

  /**
   * Set the current H3 cell for a driver
   */
  async setDriverCell(driverId: string, h3Index: string): Promise<boolean> {
    try {
      const result = await this.client.hset(
        this.DRIVER_LOCATIONS_KEY,
        driverId,
        h3Index,
      );
      this.logger.log(`Set driver ${driverId} location to cell ${h3Index}`);
      return result === 1;
    } catch (error) {
      this.handleRedisError(`setDriverCell (${driverId}, ${h3Index})`, error);
      throw error;
    }
  }

  /**
   * Get the current H3 cell for a driver
   */
  async getDriverCell(driverId: string): Promise<string | null> {
    try {
      return await this.client.hget(this.DRIVER_LOCATIONS_KEY, driverId);
    } catch (error) {
      this.handleRedisError(`getDriverCell (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Get H3 cells for multiple drivers
   */
  async getDriverCells(
    driverIds: string[],
  ): Promise<Record<string, string | null>> {
    if (driverIds.length === 0) return {};

    try {
      const results = await this.client.hmget(
        this.DRIVER_LOCATIONS_KEY,
        ...driverIds,
      );
      const mapping: Record<string, string | null> = {};

      driverIds.forEach((driverId, index) => {
        mapping[driverId] = results[index];
      });

      return mapping;
    } catch (error) {
      this.handleRedisError(
        `getDriverCells (${driverIds.length} drivers)`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove a driver's cell mapping
   */
  async removeDriverCell(driverId: string): Promise<boolean> {
    try {
      const result = await this.client.hdel(
        this.DRIVER_LOCATIONS_KEY,
        driverId,
      );
      this.logger.log(`Removed driver ${driverId} location mapping`);
      return result === 1;
    } catch (error) {
      this.handleRedisError(`removeDriverCell (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Check if a driver has a cell mapping
   */
  async hasDriverCell(driverId: string): Promise<boolean> {
    try {
      const exists = await this.client.hexists(
        this.DRIVER_LOCATIONS_KEY,
        driverId,
      );
      return exists === 1;
    } catch (error) {
      this.handleRedisError(`hasDriverCell (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Get all driver cell mappings (use with caution on large datasets)
   */
  async getAllDriverCells(): Promise<Record<string, string>> {
    try {
      return await this.client.hgetall(this.DRIVER_LOCATIONS_KEY);
    } catch (error) {
      this.handleRedisError('getAllDriverCells', error);
      throw error;
    }
  }

  /**
   * Get count of drivers with cell mappings
   */
  async getDriverCellCount(): Promise<number> {
    try {
      return await this.client.hlen(this.DRIVER_LOCATIONS_KEY);
    } catch (error) {
      this.handleRedisError('getDriverCellCount', error);
      throw error;
    }
  }

  /**
   * Batch update driver cell mappings
   */
  async setDriverCells(mappings: Record<string, string>): Promise<boolean> {
    if (Object.keys(mappings).length === 0) return true;

    try {
      const pipeline = this.client.pipeline();

      Object.entries(mappings).forEach(([driverId, h3Index]) => {
        pipeline.hset(this.DRIVER_LOCATIONS_KEY, driverId, h3Index);
      });

      const results = await pipeline.exec();
      const success = results !== null && results.every(([err]) => !err);

      this.logger.log(
        `Batch updated ${Object.keys(mappings).length} driver cell mappings`,
      );
      return success;
    } catch (error) {
      this.handleRedisError(
        `setDriverCells (${Object.keys(mappings).length} mappings)`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove multiple driver cell mappings
   */
  async removeDriverCells(driverIds: string[]): Promise<number> {
    if (driverIds.length === 0) return 0;

    try {
      const removed = await this.client.hdel(
        this.DRIVER_LOCATIONS_KEY,
        ...driverIds,
      );
      this.logger.log(`Removed ${removed} driver cell mappings`);
      return removed;
    } catch (error) {
      this.handleRedisError(
        `removeDriverCells (${driverIds.length} drivers)`,
        error,
      );
      throw error;
    }
  }
}
