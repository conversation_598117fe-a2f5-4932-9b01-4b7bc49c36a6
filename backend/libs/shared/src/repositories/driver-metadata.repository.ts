import { Injectable } from '@nestjs/common';
import { RedisService } from '../database/redis/redis.service';
import { BaseRedisRepository } from './base-redis.repository';
import { DriverMetadata } from './models/redis/driverLocation.model';

/**
 * Repository for managing driver metadata using Redis hashes.
 * Key format: drivers:{driverId}
 * Hash fields: lat, lon, h3Index, status, timestamp, city, rideId
 */
@Injectable()
export class DriverMetadataRepository extends BaseRedisRepository {
  constructor(redisService: RedisService) {
    super(redisService);
  }

  /**
   * Generate key for driver metadata
   * Format: drivers:{driverId}
   */
  private generateDriverKey(driverId: string): string {
    return this.generateKey(['drivers', driverId]);
  }

  /**
   * Set driver metadata
   */
  async setDriverMetadata(metadata: DriverMetadata): Promise<boolean> {
    try {
      const key = this.generateDriverKey(metadata.driverId);
      const hashData: Record<string, string> = {
        lat: metadata.lat.toString(),
        lon: metadata.lon.toString(),
        h3Index: metadata.h3Index,
        status: metadata.status,
        timestamp: metadata.timestamp,
        city: metadata.city,
      };

      if (metadata.rideId) {
        hashData['rideId'] = metadata.rideId;
      }

      await this.client.hset(key, hashData);

      // Set expiration to prevent memory leaks (default: 24 hours)
      await this.client.expire(key, 24 * 60 * 60);

      this.logger.log(`Set metadata for driver ${metadata.driverId}`);
      return true;
    } catch (error) {
      this.handleRedisError(`setDriverMetadata (${metadata.driverId})`, error);
      throw error;
    }
  }

  /**
   * Get driver metadata
   */
  async getDriverMetadata(driverId: string): Promise<DriverMetadata | null> {
    try {
      const key = this.generateDriverKey(driverId);
      const data = await this.client.hgetall(key);

      if (Object.keys(data).length === 0) {
        return null;
      }

      return {
        driverId,
        lat: parseFloat(data['lat']),
        lon: parseFloat(data['lon']),
        h3Index: data['h3Index'],
        status: data['status'],
        timestamp: data['timestamp'],
        city: data['city'],
        rideId: data['rideId'] || undefined,
      };
    } catch (error) {
      this.handleRedisError(`getDriverMetadata (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Get multiple driver metadata
   */
  async getDriversMetadata(
    driverIds: string[],
  ): Promise<Record<string, DriverMetadata | null>> {
    if (driverIds.length === 0) return {};

    try {
      const pipeline = this.client.pipeline();

      driverIds.forEach((driverId) => {
        const key = this.generateDriverKey(driverId);
        pipeline.hgetall(key);
      });

      const results = await pipeline.exec();
      const metadata: Record<string, DriverMetadata | null> = {};

      if (results) {
        driverIds.forEach((driverId, index) => {
          const [err, data] = results[index];

          if (!err && data && Object.keys(data as object).length > 0) {
            const hashData = data as Record<string, string>;
            metadata[driverId] = {
              driverId,
              lat: parseFloat(hashData['lat']),
              lon: parseFloat(hashData['lon']),
              h3Index: hashData['h3Index'],
              status: hashData['status'],
              timestamp: hashData['timestamp'],
              city: hashData['city'],
              rideId: hashData['rideId'] || undefined,
            };
          } else {
            metadata[driverId] = null;
          }
        });
      }

      return metadata;
    } catch (error) {
      this.handleRedisError(
        `getDriversMetadata (${driverIds.length} drivers)`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update specific fields of driver metadata
   */
  async updateDriverMetadata(
    driverId: string,
    updates: Partial<Omit<DriverMetadata, 'driverId'>>,
  ): Promise<boolean> {
    try {
      const key = this.generateDriverKey(driverId);
      const hashData: Record<string, string> = {};

      Object.entries(updates).forEach(([field, value]) => {
        if (value !== undefined) {
          if (field === 'lat' || field === 'lon') {
            hashData[field] = (value as number).toString();
          } else {
            hashData[field] = value as string;
          }
        }
      });

      if (Object.keys(hashData).length > 0) {
        await this.client.hset(key, hashData);

        // Refresh expiration
        await this.client.expire(key, 24 * 60 * 60);

        this.logger.log(`Updated metadata for driver ${driverId}`);
      }

      return true;
    } catch (error) {
      this.handleRedisError(`updateDriverMetadata (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Remove driver metadata
   */
  async removeDriverMetadata(driverId: string): Promise<boolean> {
    try {
      const key = this.generateDriverKey(driverId);
      const result = await this.client.del(key);

      this.logger.log(`Removed metadata for driver ${driverId}`);
      return result === 1;
    } catch (error) {
      this.handleRedisError(`removeDriverMetadata (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Check if driver metadata exists
   */
  async hasDriverMetadata(driverId: string): Promise<boolean> {
    try {
      const key = this.generateDriverKey(driverId);
      const exists = await this.client.exists(key);
      return exists === 1;
    } catch (error) {
      this.handleRedisError(`hasDriverMetadata (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Get driver location (lat, lon only)
   */
  async getDriverLocation(
    driverId: string,
  ): Promise<{ lat: number; lon: number } | null> {
    try {
      const key = this.generateDriverKey(driverId);
      const result = await this.client.hmget(key, 'lat', 'lon');

      if (result[0] !== null && result[1] !== null) {
        return {
          lat: parseFloat(result[0]),
          lon: parseFloat(result[1]),
        };
      }

      return null;
    } catch (error) {
      this.handleRedisError(`getDriverLocation (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Get driver status
   */
  async getDriverStatus(driverId: string): Promise<string | null> {
    try {
      const key = this.generateDriverKey(driverId);
      return await this.client.hget(key, 'status');
    } catch (error) {
      this.handleRedisError(`getDriverStatus (${driverId})`, error);
      throw error;
    }
  }

  /**
   * Set driver status
   */
  async setDriverStatus(driverId: string, status: string): Promise<boolean> {
    try {
      const key = this.generateDriverKey(driverId);
      await this.client.hset(key, 'status', status);

      // Refresh expiration
      await this.client.expire(key, 24 * 60 * 60);

      this.logger.log(`Updated status for driver ${driverId} to ${status}`);
      return true;
    } catch (error) {
      this.handleRedisError(`setDriverStatus (${driverId}, ${status})`, error);
      throw error;
    }
  }

  /**
   * Batch remove driver metadata
   */
  async removeDriversMetadata(driverIds: string[]): Promise<number> {
    if (driverIds.length === 0) return 0;

    try {
      const keys = driverIds.map((id) => this.generateDriverKey(id));
      const removed = await this.client.del(...keys);

      this.logger.log(`Removed metadata for ${removed} drivers`);
      return removed;
    } catch (error) {
      this.handleRedisError(
        `removeDriversMetadata (${driverIds.length} drivers)`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get drivers by status in a specific city
   */
  async getDriversByStatus(
    city: string,
    status: string,
    limit: number = 100,
  ): Promise<string[]> {
    try {
      // Note: This is not the most efficient way for large datasets
      // In production, consider using additional data structures for filtering
      const cursor = '0';
      const pattern = this.generateKey(['drivers', '*']);

      const result = await this.client.scan(
        cursor,
        'MATCH',
        pattern,
        'COUNT',
        limit,
      );
      const keys = result[1] as string[];

      const driverIds: string[] = [];

      if (keys.length > 0) {
        const pipeline = this.client.pipeline();
        keys.forEach((key) => {
          pipeline.hmget(key, 'city', 'status');
        });

        const results = await pipeline.exec();

        if (results) {
          keys.forEach((key, index) => {
            const [err, data] = results[index];
            const resultArray = data as string[];
            if (
              !err &&
              resultArray &&
              resultArray[0] === city &&
              resultArray[1] === status
            ) {
              const driverId = key.split(':')[1]; // Extract driverId from key
              driverIds.push(driverId);
            }
          });
        }
      }

      return driverIds;
    } catch (error) {
      this.handleRedisError(`getDriversByStatus (${city}, ${status})`, error);
      throw error;
    }
  }
}
