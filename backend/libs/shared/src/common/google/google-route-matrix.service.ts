import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { AppConfigService } from '../../config/config.service';
import axios, { AxiosResponse } from 'axios';

export interface Location {
  lat: number;
  lng: number;
}

export interface RouteMatrixRequest {
  origins: Location[];
  destinations: Location[];
  travelMode?: 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT';
  routingPreference?:
    | 'TRAFFIC_UNAWARE'
    | 'TRAFFIC_AWARE'
    | 'TRAFFIC_AWARE_OPTIMAL';
  units?: 'IMPERIAL' | 'METRIC';
}

export interface RouteMatrixElement {
  duration: {
    seconds: number;
    text: string;
  };
  distance: {
    meters: number;
    text: string;
  };
  status: 'OK' | 'NOT_FOUND' | 'ZERO_RESULTS';
  condition?: 'ROUTE_EXISTS' | 'ROUTE_NOT_FOUND';
}

export interface RouteMatrixResponse {
  originIndex: number;
  destinationIndex: number;
  status: string;
  condition?: string | undefined;
  duration?:
    | {
        seconds: number;
      }
    | undefined;
  distanceMeters?: number | undefined;
  staticDuration?:
    | {
        seconds: number;
      }
    | undefined;
  localizedValues?:
    | {
        distance: {
          text: string;
        };
        duration: {
          text: string;
        };
        staticDuration: {
          text: string;
        };
      }
    | undefined;
}

export interface GoogleRouteMatrixApiResponse {
  originIndex?: number;
  destinationIndex?: number;
  status?: string;
  condition?: string;
  duration?: {
    seconds: number;
  };
  distanceMeters?: number;
  staticDuration?: {
    seconds: number;
  };
  localizedValues?: {
    distance: {
      text: string;
    };
    duration: {
      text: string;
    };
    staticDuration: {
      text: string;
    };
  };
}

@Injectable()
export class GoogleRouteMatrixService {
  private readonly logger = new Logger(GoogleRouteMatrixService.name);
  private readonly baseUrl =
    'https://routes.googleapis.com/distanceMatrix/v2:computeRouteMatrix';

  constructor(private readonly configService: AppConfigService) {}

  /**
   * Compute route matrix using Google Routes API
   * @param request Route matrix request parameters
   * @returns Promise<RouteMatrixResponse[]>
   */
  async computeRouteMatrix(
    request: RouteMatrixRequest,
  ): Promise<RouteMatrixResponse[]> {
    try {
      this.logger.log(
        `Computing route matrix for ${request.origins.length} origins and ${request.destinations.length} destinations`,
      );

      const apiKey = this.configService.googleMapsApiKey;

      // Prepare request body according to Google Routes API v2 format
      const requestBody = {
        origins: request.origins.map((origin) => ({
          waypoint: {
            location: {
              latLng: {
                latitude: origin.lat,
                longitude: origin.lng,
              },
            },
          },
        })),
        destinations: request.destinations.map((destination) => ({
          waypoint: {
            location: {
              latLng: {
                latitude: destination.lat,
                longitude: destination.lng,
              },
            },
          },
        })),
        travelMode: request.travelMode || 'DRIVE',
        routingPreference: request.routingPreference || 'TRAFFIC_AWARE',
        units: request.units || 'METRIC',
      };

      const response: AxiosResponse<GoogleRouteMatrixApiResponse[]> =
        await axios.post(this.baseUrl, requestBody, {
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask':
              'originIndex,destinationIndex,status,condition,duration,distanceMeters,staticDuration,localizedValues.distance,localizedValues.duration,localizedValues.staticDuration',
          },
          timeout: 30000, // 30 seconds timeout
        });

      this.logger.log(
        `Route matrix API response received with ${response.data.length} elements`,
      );

      return response.data.map((element) => ({
        originIndex: element.originIndex || 0,
        destinationIndex: element.destinationIndex || 0,
        status: element.status || 'UNKNOWN',
        condition: element.condition,
        duration: element.duration,
        distanceMeters: element.distanceMeters,
        staticDuration: element.staticDuration,
        localizedValues: element.localizedValues,
      }));
    } catch (error: any) {
      this.logger.error('Failed to compute route matrix', error);

      if (error.response) {
        this.logger.error(
          `API Error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`,
        );
        throw new BadRequestException(
          `Google Routes API error: ${error.response.data?.error?.message || 'Unknown error'}`,
        );
      }

      throw new BadRequestException('Failed to compute route matrix');
    }
  }

  /**
   * Compute route matrix for drivers to pickup location
   * @param driverLocations Array of driver locations
   * @param pickupLocation Pickup location
   * @returns Promise<RouteMatrixResponse[]>
   */
  async computeDriversToPickup(
    driverLocations: Location[],
    pickupLocation: Location,
  ): Promise<RouteMatrixResponse[]> {
    if (driverLocations.length === 0) {
      return [];
    }

    return this.computeRouteMatrix({
      origins: driverLocations,
      destinations: [pickupLocation],
      travelMode: 'DRIVE',
      routingPreference: 'TRAFFIC_AWARE',
    });
  }

  /**
   * Compute route matrix from pickup to destination
   * @param pickupLocation Pickup location
   * @param destinationLocation Destination location
   * @returns Promise<RouteMatrixResponse>
   */
  async computePickupToDestination(
    pickupLocation: Location,
    destinationLocation: Location,
  ): Promise<RouteMatrixResponse | null> {
    const results = await this.computeRouteMatrix({
      origins: [pickupLocation],
      destinations: [destinationLocation],
      travelMode: 'DRIVE',
      routingPreference: 'TRAFFIC_AWARE',
    });

    return results.length > 0 ? results[0] : null;
  }

  /**
   * Batch compute route matrices with rate limiting
   * @param requests Array of route matrix requests
   * @param batchSize Number of requests to process at once
   * @param delayMs Delay between batches in milliseconds
   * @returns Promise<RouteMatrixResponse[][]>
   */
  async batchComputeRouteMatrix(
    requests: RouteMatrixRequest[],
    batchSize: number = 5,
    delayMs: number = 1000,
  ): Promise<RouteMatrixResponse[][]> {
    const results: RouteMatrixResponse[][] = [];

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchPromises = batch.map((request) =>
        this.computeRouteMatrix(request),
      );

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Add delay between batches to respect rate limits
        if (i + batchSize < requests.length) {
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        }
      } catch (error) {
        this.logger.error(
          `Batch processing failed for batch starting at index ${i}`,
          error,
        );
        throw error;
      }
    }

    return results;
  }
}
