import {
  Injectable,
  <PERSON><PERSON>,
  On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OnModuleInit,
} from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';

import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client!: Redis;

  constructor(private readonly configService: AppConfigService) {}

  onModuleInit() {
    try {
      this.client = new Redis({
        host: this.configService.redisHost,
        port: this.configService.redisPort,
        password: this.configService.redisPassword,
        db: this.configService.redisDb,
      });

      this.client.on('error', (error) => {
        this.logger.error(
          `Redis connection error: ${error.message}`,
          error.stack,
        );
      });

      this.client.on('connect', () => {
        this.logger.log('Redis connected successfully');
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to initialize Redis client: ${error.message}`,
        error.stack,
      );
    }
  }

  async onModuleDestroy() {
    if (this.client) {
      await this.client.quit();
    }
  }

  getClient(): Redis {
    return this.client;
  }

  async set(
    key: string,
    value: string,
    expireInSeconds?: number,
  ): Promise<void> {
    try {
      if (expireInSeconds) {
        await this.client.set(key, value, 'EX', expireInSeconds);
      } else {
        await this.client.set(key, value);
      }
    } catch (error: any) {
      this.logger.error(
        `Redis set error for key ${key}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error: any) {
      this.logger.error(
        `Redis get error for key ${key}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error: any) {
      this.logger.error(
        `Redis del error for key ${key}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
