import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppConfigService {
  constructor(private configService: ConfigService) {}

  // Environment
  get nodeEnv(): string {
    return this.configService.get<string>('NODE_ENV') ?? 'development';
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }

  get isTest(): boolean {
    return this.nodeEnv === 'test';
  }

  get isStaging(): boolean {
    return this.nodeEnv === 'staging' || this.nodeEnv === 'development';
  }

  // Server
  get coreApiPort(): number {
    return Number(this.configService.get<number>('CORE_API_PORT', 3000));
  }

  get coreApiHost(): string {
    return this.configService.get<string>('CORE_API_HOST', 'localhost');
  }

  // API
  get coreApiPrefix(): string {
    return this.configService.get<string>('CORE_API_PREFIX', 'api');
  }

  get coreApiVersion(): string {
    return this.configService.get<string>('CORE_API_VERSION', '1');
  }

  // CORS
  get corsOrigins(): string {
    return this.configService.get<string>('CORS_ORIGINS', '*');
  }

  // Rate Limiting
  get throttleTtl(): number {
    return Number(this.configService.get<number>('THROTTLE_TTL', 60));
  }

  get throttleLimit(): number {
    return Number(this.configService.get<number>('THROTTLE_LIMIT', 100));
  }

  // Database
  get databaseUrl(): string {
    const url = this.configService.get<string>('DATABASE_URL');
    if (!url) {
      throw new Error('DATABASE_URL is not defined');
    }
    return url;
  }

  get databaseName(): string {
    const name = this.configService.get<string>('DATABASE_NAME');
    if (!name) {
      throw new Error('DATABASE_NAME is not defined');
    }
    return name;
  }

  get databaseUser(): string {
    const user = this.configService.get<string>('DATABASE_USER');
    if (!user) {
      throw new Error('DATABASE_USER is not defined');
    }
    return user;
  }

  // get databaseHost(): string {
  //   return this.configService.get<string>('DATABASE_HOST', 'localhost');
  // }

  get databasePort(): number {
    return Number(this.configService.get<number>('DATABASE_PORT', 5432));
  }

  // Redis Configuration
  get redisHost(): string {
    return this.configService.get<string>('REDIS_HOST', 'localhost');
  }

  get redisPort(): number {
    return Number(this.configService.get<number>('REDIS_PORT', 6379));
  }

  get redisPassword(): string {
    return this.configService.get<string>('REDIS_PASSWORD', '');
  }

  get redisDb(): number {
    return Number(this.configService.get<number>('REDIS_DB', 0));
  }

  get engagespotApiKey(): string {
    const apiKey = this.configService.get<string>('ENGAGESPOT_API_KEY');
    if (!apiKey) {
      throw new Error('ENGAGESPOT_API_KEY is not defined');
    }
    return apiKey;
  }

  get engagespotApiSecret(): string {
    const apiSecret = this.configService.get<string>('ENGAGESPOT_API_SECRET');
    if (!apiSecret) {
      throw new Error('ENGAGESPOT_API_SECRET is not defined');
    }
    return apiSecret;
  }

  get verificationCodeLoginWorkflow(): string {
    const verificationCodeLoginWorkflow = this.configService.get<string>(
      'EN_VERIFICATION_CODE_WORKFLOW',
    );
    if (!verificationCodeLoginWorkflow) {
      throw new Error('EN_VERIFICATION_CODE_WORKFLOW is not defined');
    }
    return verificationCodeLoginWorkflow;
  }

  get emailVerificationCodeWorkflow(): string {
    const emailVerificationCodeWorkflow = this.configService.get<string>(
      'EN_EMAIL_VERIFICATION_CODE_WORKFLOW',
      'email-verification', // Default value if not set in environment
    );
    if (!emailVerificationCodeWorkflow) {
      throw new Error('EN_EMAIL_VERIFICATION_CODE_WORKFLOW is not defined');
    }
    return emailVerificationCodeWorkflow;
  }

  get forgotPasswordWorkflow(): string {
    const forgotPasswordWorkflow = this.configService.get<string>(
      'EN_FORGOT_PASSWORD_WORKFLOW',
      'forgot_password', // Default value if not set in environment
    );
    if (!forgotPasswordWorkflow) {
      throw new Error('EN_FORGOT_PASSWORD_WORKFLOW is not defined');
    }
    return forgotPasswordWorkflow;
  }

  // JWT Configuration
  get jwtSecret(): string {
    const secret = this.configService.get<string>('JWT_SECRET');
    if (!secret) {
      throw new Error('JWT_SECRET is not defined');
    }
    return secret;
  }

  get jwtAccessTokenExpiry(): string {
    return this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRY', '15m');
  }

  get jwtRefreshTokenExpiry(): string {
    return this.configService.get<string>('JWT_REFRESH_TOKEN_EXPIRY', '7d');
  }

  // Google OAuth Configuration
  get googleClientId(): string {
    const clientId = this.configService.get<string>('GOOGLE_CLIENT_ID');
    if (!clientId) {
      throw new Error('GOOGLE_CLIENT_ID is not defined');
    }
    return clientId;
  }

  get googleClientSecret(): string {
    const clientSecret = this.configService.get<string>('GOOGLE_CLIENT_SECRET');
    if (!clientSecret) {
      throw new Error('GOOGLE_CLIENT_SECRET is not defined');
    }
    return clientSecret;
  }

  // Google Maps API Configuration
  get googleMapsApiKey(): string {
    const apiKey = this.configService.get<string>('GOOGLE_MAPS_API_KEY');
    if (!apiKey) {
      throw new Error('GOOGLE_MAPS_API_KEY is not defined');
    }
    return apiKey;
  }

  get googleClientIdIos(): string {
    const clientIdIos = this.configService.get<string>('GOOGLE_CLIENT_ID_IOS');
    if (!clientIdIos) {
      throw new Error('GOOGLE_CLIENT_ID_IOS is not defined');
    }
    return clientIdIos;
  }

  // Apple OAuth Configuration
  get appleClientId(): string {
    const clientId = this.configService.get<string>('APPLE_CLIENT_ID');
    if (!clientId) {
      throw new Error('APPLE_CLIENT_ID is not defined');
    }
    return clientId;
  }

  get appleTeamId(): string {
    const teamId = this.configService.get<string>('APPLE_TEAM_ID');
    if (!teamId) {
      throw new Error('APPLE_TEAM_ID is not defined');
    }
    return teamId;
  }

  get appleKeyId(): string {
    const keyId = this.configService.get<string>('APPLE_KEY_ID');
    if (!keyId) {
      throw new Error('APPLE_KEY_ID is not defined');
    }
    return keyId;
  }

  get applePrivateKey(): string {
    const privateKey = this.configService.get<string>('APPLE_PRIVATE_KEY');
    if (!privateKey) {
      throw new Error('APPLE_PRIVATE_KEY is not defined');
    }
    return privateKey.replace(/\\n/g, '\n'); // Handle escaped newlines
  }

  get appleBundleId(): string {
    const bundleId = this.configService.get<string>('APPLE_BUNDLE_ID');
    if (!bundleId) {
      throw new Error('APPLE_BUNDLE_ID is not defined');
    }
    return bundleId;
  }

  // OTP Configuration
  get otpLength(): number {
    return Number(this.configService.get<number>('OTP_LENGTH', 6));
  }

  get otpExpiryMinutes(): number {
    return Number(this.configService.get<number>('OTP_EXPIRY_MINUTES', 5));
  }

  get otpWindow(): number {
    return Number(this.configService.get<number>('OTP_WINDOW', 1));
  }

  // Rate Limiting for Auth
  get authRateLimitTtl(): number {
    return Number(this.configService.get<number>('AUTH_RATE_LIMIT_TTL', 900)); // 15 minutes
  }

  get authRateLimitMax(): number {
    return Number(this.configService.get<number>('AUTH_RATE_LIMIT_MAX', 5)); // 5 attempts
  }

  // AWS Configuration
  get awsAccessKeyId(): string {
    const key = this.configService.get<string>('AWS_ACCESS_KEY_ID');
    if (!key) {
      throw new Error('AWS_ACCESS_KEY_ID is not defined');
    }
    return key;
  }

  get awsSecretAccessKey(): string {
    const secret = this.configService.get<string>('AWS_SECRET_ACCESS_KEY');
    if (!secret) {
      throw new Error('AWS_SECRET_ACCESS_KEY is not defined');
    }
    return secret;
  }

  get awsRegion(): string {
    const region = this.configService.get<string>('AWS_REGION');
    if (!region) {
      throw new Error('AWS_REGION is not defined');
    }
    return region;
  }

  get awsBucketName(): string {
    const bucket = this.configService.get<string>('AWS_BUCKET_NAME');
    if (!bucket) {
      throw new Error('AWS_BUCKET_NAME is not defined');
    }
    return bucket;
  }

  // Cashfree Configuration
  get cashfreeBaseUrl(): string {
    const url = this.configService.get<string>('CASHFREE_BASE_URL');
    if (!url) {
      throw new Error('CASHFREE_BASE_URL is not defined');
    }
    return url;
  }

  get cashfreeApiSecret(): string {
    const secret = this.configService.get<string>('CASHFREE_CLIENT_SECRET');
    if (!secret) {
      throw new Error('CASHFREE_CLIENT_SECRET is not defined');
    }
    return secret;
  }

  get cashfreeApiId(): string {
    const id = this.configService.get<string>('CASHFREE_CLIENT_ID');
    if (!id) {
      throw new Error('CASHFREE_CLIENT_ID is not defined');
    }
    return id;
  }

  get cashfreeEnvironment(): string {
    return this.configService.get<string>('CASHFREE_ENVIRONMENT', 'sandbox');
  }

  // Microservices Configuration

  // Kafka Configuration
  get kafkaBrokers(): string[] {
    const brokers = this.configService.get<string>(
      'KAFKA_BROKERS',
      'localhost:9092',
    );
    return brokers.split(',').map((broker) => broker.trim());
  }

  get kafkaClientId(): string {
    return this.configService.get<string>(
      'KAFKA_CLIENT_ID',
      'tukxi-kafka-client',
    );
  }

  get kafkaGroupId(): string {
    return this.configService.get<string>(
      'KAFKA_GROUP_ID',
      'tukxi-consumer-group',
    );
  }

  get kafkaRetryAttempts(): number {
    return Number(this.configService.get<number>('KAFKA_RETRY_ATTEMPTS', 5));
  }

  get kafkaRetryDelay(): number {
    return Number(this.configService.get<number>('KAFKA_RETRY_DELAY', 1000));
  }

  get kafkaConnectionTimeout(): number {
    return Number(
      this.configService.get<number>('KAFKA_CONNECTION_TIMEOUT', 10000),
    );
  }

  get kafkaRequestTimeout(): number {
    return Number(
      this.configService.get<number>('KAFKA_REQUEST_TIMEOUT', 30000),
    );
  }

  // RabbitMQ Configuration
  get rabbitmqUrl(): string {
    return this.configService.get<string>(
      'RABBITMQ_URL',
      'amqp://localhost:5672',
    );
  }

  get rabbitmqQueue(): string {
    return this.configService.get<string>('RABBITMQ_QUEUE', 'tukxi_queue');
  }

  get rabbitmqExchange(): string {
    return this.configService.get<string>(
      'RABBITMQ_EXCHANGE',
      'tukxi_exchange',
    );
  }

  get rabbitmqRoutingKey(): string {
    return this.configService.get<string>(
      'RABBITMQ_ROUTING_KEY',
      'tukxi.ride.match',
    );
  }

  get rabbitmqDurable(): boolean {
    return this.configService.get<boolean>('RABBITMQ_DURABLE', true);
  }

  get rabbitmqPrefetchCount(): number {
    return Number(
      this.configService.get<number>('RABBITMQ_PREFETCH_COUNT', 10),
    );
  }

  // Microservice Ports
  get kafkaConsumerPort(): number {
    return Number(this.configService.get<number>('KAFKA_CONSUMER_PORT', 3001));
  }

  get notifierPort(): number {
    return Number(this.configService.get<number>('NOTIFIER_PORT', 3002));
  }

  get rideMatcherPort(): number {
    return Number(this.configService.get<number>('RIDE_MATCHER_PORT', 3003));
  }

  // Health Check Configuration
  get healthCheckTimeout(): number {
    return Number(this.configService.get<number>('HEALTH_CHECK_TIMEOUT', 5000));
  }

  get healthCheckInterval(): number {
    return Number(
      this.configService.get<number>('HEALTH_CHECK_INTERVAL', 30000),
    );
  }

  // Logging Configuration
  get logLevel(): string {
    return this.configService.get<string>('LOG_LEVEL', 'info');
  }

  get logFormat(): string {
    return this.configService.get<string>('LOG_FORMAT', 'json');
  }

  // Graceful Shutdown Configuration
  get shutdownTimeout(): number {
    return Number(this.configService.get<number>('SHUTDOWN_TIMEOUT', 10000));
  }
}
