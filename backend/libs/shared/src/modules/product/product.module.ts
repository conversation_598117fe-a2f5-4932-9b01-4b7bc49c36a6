import { Module } from '@nestjs/common';
import { ProductService } from './product.service';
import { ProductRepository } from '../../repositories/product.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { VehicleTypeModule } from '../vehicle-type/vehicle-type.module';
import { ProductServiceModule } from '../product-service/product-service.module';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule } from '../../config';

@Module({
  imports: [VehicleTypeModule, ProductServiceModule, AppConfigModule],
  providers: [
    ProductService,
    ProductRepository,
    PrismaService,
    FileUploadService,
  ],
  exports: [ProductService, ProductRepository],
})
export class ProductModule {}
