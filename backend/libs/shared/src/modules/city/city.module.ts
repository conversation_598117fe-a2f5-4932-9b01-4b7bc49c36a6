import { Module } from '@nestjs/common';
import { CityRepository } from '@shared/shared/repositories/city.repository';
import { CityService } from './city.service';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule } from '@shared/shared/config';
import { ZoneModule } from '../zone/zone.module';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';

@Module({
  imports: [AppConfigModule, ZoneModule],
  providers: [
    CityRepository,
    CityService,
    PrismaService,
    FileUploadService,
    H3UtilityService,
  ],
  exports: [CityRepository, CityService, H3UtilityService],
})
export class CityModule {}
