import { Modu<PERSON> } from '@nestjs/common';
import { PrismaService } from '../../database/prisma/prisma.service';
import { VehicleTypeService } from './vehicle-type.service';
import { VehicleTypeRepository } from '@shared/shared/repositories/vehicle-type.repository';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule } from '../../config';

@Module({
  imports: [AppConfigModule],
  providers: [
    VehicleTypeService,
    PrismaService,
    VehicleTypeRepository,
    FileUploadService,
  ],
  exports: [VehicleTypeService],
})
export class VehicleTypeModule {}
