import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { VehicleType } from '@shared/shared/repositories/models/vehicleType.model';
import { VehicleTypeRepository } from '@shared/shared/repositories/vehicle-type.repository';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class VehicleTypeService {
  constructor(
    private readonly vehicleTypeRepository: VehicleTypeRepository,
    private readonly fileUploadService: FileUploadService,
  ) {}

  /**
   * Create a new vehicle record.
   * @param data - Vehicle data excluding id and timestamps
   */
  async createVehicleType(
    data: Omit<VehicleType, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<VehicleType> {
    // Check for duplicate name (case-insensitive)
    const existing = await this.vehicleTypeRepository.findOne({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive',
        },
      },
    });
    if (existing) {
      throw new BadRequestException(
        `Vehicle category with name "${data.name}" already exists.`,
      );
    }
    return this.vehicleTypeRepository.createVehicleType(data);
  }

  /**
   * Retrieve all vehicles from the repository.
   */
  async findAllVehicleTypes(): Promise<VehicleType[]> {
    const vehicleTypes = await this.vehicleTypeRepository.findAllVehicleTypes();
    return this.addSignedUrlsToVehicleTypes(vehicleTypes);
  }

  /**
   * Find a vehicle by its ID. Throws if not found.
   * @param id - Vehicle ID
   */
  async findVehicleTypeById(id: string): Promise<VehicleType> {
    const vehicle = await this.vehicleTypeRepository.findVehicleTypeById(id);
    if (!vehicle)
      throw new NotFoundException(`Vehicle with ID ${id} not found`);
    return this.addSignedUrlsToVehicleTypes(vehicle);
  }

  /**
   * Update an existing vehicle by ID.
   * @param id - Vehicle ID
   * @param data - Partial vehicle data to update
   */
  async updateVehicleType(
    id: string,
    data: Partial<VehicleType>,
  ): Promise<VehicleType> {
    await this.findVehicleTypeById(id);
    if (data.name) {
      // Check for duplicate name (case-insensitive), excluding current id
      const existing = await this.vehicleTypeRepository.findOne({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive',
          },
        },
        select: { id: true },
      });
      if (existing && existing.id !== id) {
        throw new BadRequestException(
          `Vehicle type with name "${data.name}" already exists.`,
        );
      }
    }
    return this.vehicleTypeRepository.updateVehicleType(id, data);
  }

  /**
   * Delete a vehicle by ID after ensuring it exists.
   * @param id - Vehicle ID
   */
  async deleteVehicleType(id: string): Promise<VehicleType> {
    await this.findVehicleTypeById(id);
    return this.vehicleTypeRepository.deleteVehicleType(id);
  }

  /**
   * Paginate vehicles with optional search and sorting.
   * @param page - Page number (default 1)
   * @param limit - Items per page (default 10)
   * @param dto - Pagination and filter options
   */
  async paginateVehicleTypes(page = 1, limit = 10, dto?: PaginationDto) {
    const options = this.buildPaginateOptions(dto);
    // console.log(options)
    const result = await this.vehicleTypeRepository.paginateVehicleTypes(
      page,
      limit,
      options,
    );

    // Add signed URLs to the data array
    if (result.data && Array.isArray(result.data)) {
      result.data = await this.addSignedUrlsToVehicleTypes(result.data);
    }

    return result;
  }

  /**
   * Build options for pagination, supporting search by name and sorting.
   * @param dto - PaginationDto containing search and sort options
   * @returns Options object for repository pagination
   */
  private buildPaginateOptions(dto?: PaginationDto) {
    const options: any = {};
    if (dto) {
      // Add search by vehicle name (case-insensitive)
      if (dto.search) {
        options.where = {
          name: {
            contains: dto.search,
            mode: 'insensitive',
          },
        };
      }
      // Add sorting if specified
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }
    return options;
  }

  /**
   * Fetch all vehicles associated with a given city_id.
   * @param cityId - The ID of the city
   * @param languageCode - Optional language code for translation
   */
  async findActiveVehicleTypesByCityId(
    cityId: string,
    languageCode?: string,
  ): Promise<VehicleType[]> {
    const vehicleTypes =
      await this.vehicleTypeRepository.findActiveVehicleTypesByCityId(cityId);

    let processedVehicleTypes =
      await this.addSignedUrlsToVehicleTypes(vehicleTypes);

    // Apply language translation if language code is provided
    if (languageCode) {
      processedVehicleTypes = this.applyLanguageTranslation(
        processedVehicleTypes,
        languageCode,
      );
    } else {
      // Remove languageSpec from response when no translation is applied
      processedVehicleTypes = processedVehicleTypes.map((vehicleType) => {
        const { languageSpec, ...rest } = vehicleType;
        return rest;
      });
    }
    return processedVehicleTypes;
  }

  /**
   * Helper method to add signed URLs to vehicle types
   * @param vehicleTypes - Array of vehicle types or single vehicle type
   * @returns Vehicle types with signed URLs for images
   */
  private async addSignedUrlsToVehicleTypes<
    T extends VehicleType | VehicleType[],
  >(vehicleTypes: T): Promise<T> {
    if (Array.isArray(vehicleTypes)) {
      return Promise.all(
        vehicleTypes.map(async (vehicleType) => {
          if (vehicleType.image) {
            try {
              const signedUrl = await this.fileUploadService.getSignedUrl(
                vehicleType.image,
                3600, // 1 hour expiry
              );
              return { ...vehicleType, image: signedUrl };
            } catch (error) {
              // If signed URL generation fails, keep the original URL
              return vehicleType;
            }
          }
          return vehicleType;
        }),
      ) as Promise<T>;
    } else {
      if (vehicleTypes.image) {
        try {
          const signedUrl = await this.fileUploadService.getSignedUrl(
            vehicleTypes.image,
            3600, // 1 hour expiry
          );
          return { ...vehicleTypes, image: signedUrl } as T;
        } catch (error) {
          // If signed URL generation fails, keep the original URL
          return vehicleTypes;
        }
      }
      return vehicleTypes;
    }
  }

  /**
   * Apply language translation to vehicle types based on language_spec
   * @param vehicleTypes - Vehicle types to translate
   * @param languageCode - Language code for translation
   */
  private applyLanguageTranslation<T extends VehicleType | VehicleType[]>(
    vehicleTypes: T,
    languageCode: string,
  ): T {
    if (Array.isArray(vehicleTypes)) {
      return vehicleTypes.map((vehicleType) =>
        this.translateSingleVehicleType(vehicleType, languageCode),
      ) as T;
    } else {
      return this.translateSingleVehicleType(vehicleTypes, languageCode) as T;
    }
  }

  /**
   * Translate a single vehicle type based on language_spec
   * @param vehicleType - Vehicle type to translate
   * @param languageCode - Language code for translation
   */
  private translateSingleVehicleType(
    vehicleType: VehicleType,
    languageCode: string,
  ): VehicleType {
    if (
      !vehicleType.languageSpec ||
      typeof vehicleType.languageSpec !== 'object'
    ) {
      return vehicleType;
    }

    const translatedVehicleType = { ...vehicleType };
    const languageSpec = vehicleType.languageSpec as any;

    // Apply translations for each field in language_spec
    Object.keys(languageSpec).forEach((field) => {
      const translations = languageSpec[field];
      if (
        translations &&
        typeof translations === 'object' &&
        translations[languageCode]
      ) {
        // Override the original field with the translated value
        (translatedVehicleType as any)[field] = translations[languageCode];
      }
    });

    // Remove languageSpec from response when translation is applied
    delete translatedVehicleType.languageSpec;

    return translatedVehicleType;
  }
}
