import { Injectable, NotFoundException } from '@nestjs/common';
import { Zone } from '@shared/shared/repositories/models/zone.model';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { ZoneRepository } from '@shared/shared/repositories/zone.repository';

@Injectable()
export class ZoneService {
  constructor(
    private readonly zoneRepository: ZoneRepository,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly prisma: PrismaService,
    private readonly h3UtilityService: H3UtilityService,
  ) {}

  async createZone(
    data: Omit<Zone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Zone> {
    return this.prisma.$transaction(async () => {
      // Create the zone
      const zone = await this.zoneRepository.createZone(data);

      // Create H3 index mappings (convert strings to BigInt for lookup table)
      if (data.h3Indexes && data.h3Indexes.length > 0) {
        const h3BigIntIndexes = this.h3UtilityService.stringArrayToBigIntArray(
          data.h3Indexes,
        );
        await this.h3IndexToZoneRepository.createManyH3IndexToZones(
          h3BigIntIndexes.map((h3Index) => ({
            h3Index,
            zoneId: zone.id,
          })),
        );
      }

      return zone;
    });
  }

  async createCityZone(
    name: string,
    polygon: any,
    h3Indexes: string[],
  ): Promise<Zone> {
    const zoneData = {
      name,
      isCity: true,
      polygon,
      h3Indexes,
      cityId: null, // City zones have cityId as null
    };

    return this.createZone(zoneData);
  }

  async findAllZones(): Promise<Zone[]> {
    return this.zoneRepository.findAllZones();
  }

  async findZoneById(id: string): Promise<Zone> {
    const zone = await this.zoneRepository.findZoneById(id);
    if (!zone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return zone;
  }

  async findZonesByCity(cityId: string): Promise<Zone[]> {
    return this.zoneRepository.findZonesByCity(cityId);
  }

  async findCityZone(cityId: string): Promise<Zone | null> {
    return this.zoneRepository.findCityZone(cityId);
  }

  async findZonesByH3Index(h3Index: string): Promise<Zone[]> {
    return this.zoneRepository.findZonesByH3Index(h3Index);
  }

  async updateZone(id: string, data: Partial<Zone>): Promise<Zone> {
    const existingZone = await this.zoneRepository.findZoneById(id);
    if (!existingZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    return this.prisma.$transaction(async () => {
      // Update the zone
      const updatedZone = await this.zoneRepository.updateZone(id, data);

      // Update H3 index mappings if h3Indexes are provided
      if (data.h3Indexes !== undefined) {
        const h3BigIntIndexes = this.h3UtilityService.stringArrayToBigIntArray(
          data.h3Indexes,
        );
        await this.h3IndexToZoneRepository.bulkUpsertH3IndexToZones(
          id,
          h3BigIntIndexes,
        );
      }

      return updatedZone;
    });
  }

  async deleteZone(id: string): Promise<Zone> {
    const existingZone = await this.zoneRepository.findZoneById(id);
    if (!existingZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    return this.prisma.$transaction(async () => {
      // Delete H3 index mappings first
      await this.h3IndexToZoneRepository.deleteH3IndexToZonesByZone(id);

      // Then delete the zone
      return this.zoneRepository.deleteZone(id);
    });
  }

  async paginateZones(
    page = 1,
    limit = 10,
    filters?: {
      isCity?: boolean;
      cityId?: string;
      search?: string;
    },
  ) {
    const options: any = {};

    if (filters) {
      const whereConditions: any = {};

      if (filters.isCity !== undefined) {
        whereConditions.isCity = filters.isCity;
      }

      if (filters.cityId) {
        whereConditions.cityId = filters.cityId;
      }

      if (filters.search) {
        whereConditions.name = {
          contains: filters.search,
          mode: 'insensitive',
        };
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }
    }

    return this.zoneRepository.paginateZones(page, limit, options);
  }
}
