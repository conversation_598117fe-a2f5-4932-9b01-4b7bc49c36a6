import { Modu<PERSON> } from '@nestjs/common';
import { ZoneService } from './zone.service';
import { ZoneRepository } from '@shared/shared/repositories/zone.repository';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';

@Module({
  providers: [
    ZoneService,
    ZoneRepository,
    H3IndexToZoneRepository,
    PrismaService,
    H3UtilityService,
  ],
  exports: [ZoneService, ZoneRepository, H3IndexToZoneRepository],
})
export class ZoneModule {}
