import { Module } from '@nestjs/common';
import { RideSearchService } from './ride-search.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { LocationIngestorService } from '../location/location-ingestor.service';
import { ProductModule } from '../product/product.module';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { CellDriverRepository } from '@shared/shared/repositories/cell-driver.repository';

import { DriverMetadataRepository } from '@shared/shared/repositories/driver-metadata.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { RedisService } from '@shared/shared/database/redis/redis.service';
import { GoogleRouteMatrixService } from '@shared/shared/common/google/google-route-matrix.service';
import { AppConfigModule } from '@shared/shared/config';

@Module({
  imports: [ProductModule,AppConfigModule],
  providers: [
    RideSearchService,
    H3UtilityService,
    H3IndexToZoneRepository,
    LocationIngestorService,
    FileUploadService,
    CellDriverRepository,
    DriverMetadataRepository,
    PrismaService,
    RedisService,
    GoogleRouteMatrixService,
  ],
  exports: [RideSearchService],
})
export class RideSearchModule {}
