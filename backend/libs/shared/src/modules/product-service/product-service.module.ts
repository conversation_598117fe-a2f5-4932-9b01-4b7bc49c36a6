import { Module } from '@nestjs/common';
import { PrismaService } from '../../database/prisma/prisma.service';
import { ProductServiceService } from './product-service.service';
import { ProductServiceRepository } from '../../repositories/product-service.repository';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule } from '../../config';

@Module({
  imports: [AppConfigModule],
  providers: [
    ProductServiceService,
    PrismaService,
    ProductServiceRepository,
    FileUploadService,
  ],
  exports: [ProductServiceService],
})
export class ProductServiceModule {}
