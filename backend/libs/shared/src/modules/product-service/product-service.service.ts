import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ProductServiceRepository } from '../../repositories/product-service.repository';
import { ProductService } from '../../repositories/models/productService.model';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class ProductServiceService {
  constructor(
    private readonly productServiceRepository: ProductServiceRepository,
    private readonly fileUploadService: FileUploadService,
  ) {}

  /**
   * Create a new product service
   */
  async createProductService(
    data: Omit<ProductService, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<ProductService> {
    // Check for duplicate name
    const existingByName = await this.productServiceRepository.findOne({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive',
        },
      },
    });
    if (existingByName) {
      throw new BadRequestException(
        `Product service with name "${data.name}" already exists`,
      );
    }

    // Generate identifier if not provided
    let identifier = data.identifier;
    if (!identifier) {
      identifier = data.name.toLowerCase().replace(/\s+/g, '_');
    }

    // Check for duplicate identifier
    const existingByIdentifier =
      await this.productServiceRepository.findProductServiceByIdentifier(
        identifier,
      );
    if (existingByIdentifier) {
      throw new BadRequestException(
        `Product service with identifier "${identifier}" already exists`,
      );
    }

    const productService =
      await this.productServiceRepository.createProductService({
        ...data,
        identifier,
      });

    return this.addSignedUrlToProductService(productService);
  }

  /**
   * Find all product services
   */
  async findAllProductServices(): Promise<ProductService[]> {
    const productServices =
      await this.productServiceRepository.findAllProductServices();
    return this.addSignedUrlsToProductServices(productServices);
  }

  /**
   * Find product service by ID
   */
  async findProductServiceById(id: string): Promise<ProductService> {
    const productService =
      await this.productServiceRepository.findProductServiceById(id);
    if (!productService) {
      throw new NotFoundException(`Product service with ID ${id} not found`);
    }
    return this.addSignedUrlToProductService(productService);
  }

  /**
   * Update product service
   */
  async updateProductService(
    id: string,
    data: Partial<ProductService>,
  ): Promise<ProductService> {
    // Check if product service exists
    const existingProductService =
      await this.productServiceRepository.findProductServiceById(id);
    if (!existingProductService) {
      throw new NotFoundException(`Product service with ID ${id} not found`);
    }

    // Check for duplicate name (excluding current record)
    if (data.name) {
      const existingByName = await this.productServiceRepository.findOne({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive',
          },
        },
        select: { id: true },
      });
      if (existingByName && existingByName.id !== id) {
        throw new BadRequestException(
          `Product service with name "${data.name}" already exists`,
        );
      }
    }

    // Don't allow identifier to be changed
    if (data.identifier !== undefined) {
      delete data.identifier;
    }

    // Handle icon deletion if icon is being updated and old icon exists
    if (
      data.icon !== undefined &&
      existingProductService.icon &&
      data.icon !== existingProductService.icon
    ) {
      try {
        await this.fileUploadService.deleteFile(existingProductService.icon);
      } catch (error) {
        console.warn(
          `Failed to delete old icon file: ${existingProductService.icon}`,
          error,
        );
      }
    }

    const updatedProductService =
      await this.productServiceRepository.updateProductService(id, data);
    return this.addSignedUrlToProductService(updatedProductService);
  }

  /**
   * Delete product service (soft delete)
   */
  async deleteProductService(id: string): Promise<ProductService> {
    const productService =
      await this.productServiceRepository.findProductServiceById(id);
    if (!productService) {
      throw new NotFoundException(`Product service with ID ${id} not found`);
    }
    return this.productServiceRepository.deleteProductService(id);
  }

  /**
   * Permanently delete product service
   */
  async permanentDeleteProductService(id: string): Promise<ProductService> {
    const productService =
      await this.productServiceRepository.findProductServiceById(id);
    if (!productService) {
      throw new NotFoundException(`Product service with ID ${id} not found`);
    }

    // Delete icon file if exists
    if (productService.icon) {
      try {
        await this.fileUploadService.deleteFile(productService.icon);
      } catch (error) {
        console.warn(
          `Failed to delete icon file: ${productService.icon}`,
          error,
        );
      }
    }

    return this.productServiceRepository.permanentDeleteProductService(id);
  }

  /**
   * Helper method to add signed URL to single product service
   */
  private async addSignedUrlToProductService(
    productService: ProductService,
  ): Promise<ProductService> {
    if (productService.icon) {
      try {
        const signedUrl = await this.fileUploadService.getSignedUrl(
          productService.icon,
          3600, // 1 hour expiry
        );
        return { ...productService, icon: signedUrl };
      } catch (error) {
        // If signed URL generation fails, keep the original URL
        return productService;
      }
    }
    return productService;
  }

  /**
   * Helper method to add signed URLs to array of product services
   */
  private async addSignedUrlsToProductServices(
    productServices: ProductService[],
  ): Promise<ProductService[]> {
    return Promise.all(
      productServices.map(async (productService) => {
        return this.addSignedUrlToProductService(productService);
      }),
    );
  }
}
