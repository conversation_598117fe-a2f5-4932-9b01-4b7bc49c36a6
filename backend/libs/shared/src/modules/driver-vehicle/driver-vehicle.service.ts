import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import {
  DriverVehicle,
  DriverVehicleStatus,
} from '../../repositories/models/driverVehicle.model';
import { UserProfileService } from '../user-profile/user-profile.service';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { DriverVehicleDocumentService } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.service';

@Injectable()
export class DriverVehicleService {
  constructor(
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly userProfileService: UserProfileService,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly driverVehicleDocumentService: DriverVehicleDocumentService,
  ) {}

  async createDriverVehicle(userId: string, dto: any): Promise<DriverVehicle> {
    // Map DTO to repository input, ensuring userId is set
    const userProfile =
      await this.userProfileService.findDriverProfileByUserId(userId);
    if (!userProfile) {
      throw new NotFoundException(
        `User profile for user ID ${userId} not found`,
      );
    }
    // need to update if profileid and vehicle type id record exist
    const existingVehicle = await this.driverVehicleRepository.findOne({
      where: {
        userProfileId: userProfile.id,
        vehicleTypeId: dto.vehicleTypeId,
      },
    });
    if (existingVehicle) {
      // Update the existing vehicle record instead of creating a new one
      return this.driverVehicleRepository.updateDriverVehicle(
        existingVehicle.id,
        {
          ...dto,
          userProfileId: userProfile.id,
        },
      );
    }
    await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
      userProfile.userId,
      userProfile.roleId,
      OnboardingStep.VEHICLE_REGISTRATION,
    );
    // Create a new driver vehicle record
    return this.driverVehicleRepository.createDriverVehicle({
      ...dto,
      userProfileId: userProfile.id,
    });
  }

  async deleteDriverVehicle(id: string): Promise<DriverVehicle> {
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(id);
    if (!driverVehicle) {
      throw new NotFoundException(`DriverVehicle with ID ${id} not found`);
    }
    return this.driverVehicleRepository.deleteDriverVehicle(id);
  }

  /**
   * Find driver vehicles by user profile ID with vehicle type relation
   */
  async findDriverVehiclesByUserProfileId(
    userProfileId: string,
  ): Promise<DriverVehicle[]> {
    // Validate that the user profile exists
    await this.userProfileService.findUserProfileById(userProfileId);

    return this.driverVehicleRepository.findMany({
      where: {
        userProfileId,
      },
      include: {
        vehicleType: true,
        _count: {
          select: {
            driverVehicleDocuments: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Find driver vehicle by ID with relations
   */
  async findDriverVehicleByIdWithRelations(id: string): Promise<DriverVehicle> {
    const driverVehicle = await this.driverVehicleRepository.findById(id, {
      include: {
        vehicleType: true,
        cityProduct: true,
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!driverVehicle) {
      throw new NotFoundException(`Driver vehicle with ID ${id} not found`);
    }

    return driverVehicle;
  }

  // ==================== ADMIN METHODS ====================

  /**
   * Create driver vehicle (Admin)
   * @param data Driver vehicle data with profileId
   * @returns Created driver vehicle
   */
  async createDriverVehicleForAdmin(data: {
    profileId: string;
    vehicleTypeId: string;
    vehicleNumber: string;
    cityProductId?: string;
    isNocRequired?: boolean;
    isPrimary?: boolean;
  }): Promise<DriverVehicle> {
    const { profileId, ...vehicleData } = data;

    // Validate that the profile exists
    const userProfile =
      await this.userProfileService.findUserProfileById(profileId);
    if (!userProfile) {
      throw new NotFoundException(
        `User profile with ID ${profileId} not found`,
      );
    }

    // Check if a vehicle with the same vehicle type already exists for this profile
    const existingVehicle = await this.driverVehicleRepository.findOne({
      where: {
        userProfileId: profileId,
        vehicleNumber: data.vehicleNumber,
      },
    });

    if (existingVehicle) {
      throw new BadRequestException(
        `A vehicle with this vehicle number already exists for this profile`,
      );
    }

    if (vehicleData.isPrimary) {
      // Set all other vehicles for this profile to isPrimary false
      await this.driverVehicleRepository.updateMany(
        {
          userProfileId: profileId,
          isPrimary: true,
        },
        {
          isPrimary: false,
        },
      );
    }
    // Create the driver vehicle
    return this.driverVehicleRepository.createDriverVehicle({
      ...vehicleData,
      userProfileId: profileId,
      status: DriverVehicleStatus.pending,
      isNocRequired: vehicleData.isNocRequired || false,
      isPrimary: vehicleData.isPrimary || false,
    });
  }

  /**
   * Update driver vehicle (Admin)
   * @param id Driver vehicle ID
   * @param data Update data
   * @returns Updated driver vehicle
   */
  async updateDriverVehicleForAdmin(
    id: string,
    data: Partial<{
      vehicleTypeId: string;
      vehicleNumber: string;
      cityProductId: string;
      isNocRequired: boolean;
      isPrimary: boolean;
      status: DriverVehicleStatus;
    }>,
  ): Promise<DriverVehicle> {
    // Check if driver vehicle exists
    const existingVehicle = await this.driverVehicleRepository.findById(id);
    if (!existingVehicle) {
      throw new NotFoundException(`Driver vehicle with ID ${id} not found`);
    }

    // If updating vehicle type, check for conflicts
    if (
      data.vehicleTypeId &&
      data.vehicleTypeId !== existingVehicle.vehicleTypeId
    ) {
      const conflictingVehicle = await this.driverVehicleRepository.findOne({
        where: {
          userProfileId: existingVehicle.userProfileId,
          vehicleNumber: data.vehicleNumber,
          id: { not: id }, // Exclude current vehicle
        },
      });

      if (conflictingVehicle) {
        throw new BadRequestException(
          `A vehicle with same vehicle number already exists for this profile`,
        );
      }
    }
    if (data.isPrimary) {
      // Set all other vehicles for this profile to isPrimary false
      await this.driverVehicleRepository.updateMany(
        {
          userProfileId: existingVehicle.userProfileId,
          isPrimary: true,
        },
        {
          isPrimary: false,
        },
      );
    }
    // Update the driver vehicle
    return this.driverVehicleRepository.updateDriverVehicle(id, data);
  }

  /**
   * Change driver vehicle status (Admin only)
   * @param id Driver vehicle ID
   * @param newStatus New status to set
   * @returns Status change result
   */
  async changeDriverVehicleStatus(
    id: string,
    newStatus: DriverVehicleStatus,
  ): Promise<{
    id: string;
    status: DriverVehicleStatus;
    previousStatus: DriverVehicleStatus;
    message: string;
    updatedAt: Date;
  }> {
    // Find driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(id);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    const previousStatus = driverVehicle.status;

    // If trying to set the same status, return current vehicle
    if (previousStatus === newStatus) {
      return {
        id: driverVehicle.id,
        status: previousStatus,
        previousStatus: previousStatus,
        message: `Driver vehicle is already in ${newStatus} status`,
        updatedAt: driverVehicle.updatedAt,
      };
    }

    // Validation for verifying vehicle
    if (newStatus === DriverVehicleStatus.active) {
      let identifiers = ['vehicle_registration', 'insurance'];
      if (driverVehicle.isNocRequired) {
        identifiers.push('noc');
      }
      const allDocumentsApproved =
        await this.driverVehicleDocumentService.areAllMandatoryDocumentsApproved(
          id,
          identifiers,
        );
      if (!allDocumentsApproved) {
        throw new BadRequestException(
          'Cannot verify vehicle: All mandatory documents must be approved first',
        );
      }
    }

    // Update driver vehicle status
    const updatedVehicle =
      await this.driverVehicleRepository.updateDriverVehicle(id, {
        status: newStatus,
      });

    return {
      id: updatedVehicle.id,
      status: newStatus,
      previousStatus: previousStatus,
      message: `Driver vehicle status changed from ${previousStatus} to ${newStatus} successfully`,
      updatedAt: updatedVehicle.updatedAt,
    };
  }
}
