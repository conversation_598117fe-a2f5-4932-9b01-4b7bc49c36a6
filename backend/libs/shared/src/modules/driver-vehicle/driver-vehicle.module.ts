import { Module } from '@nestjs/common';
import { DriverVehicleService } from './driver-vehicle.service';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { UserProfileModule } from '../user-profile/user-profile.module';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { DriverVehicleDocumentModule } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.module';

@Module({
  imports: [
    UserProfileModule,
    UserOnboardingModule,
    DriverVehicleDocumentModule,
  ],
  providers: [DriverVehicleService, DriverVehicleRepository, PrismaService],
  exports: [DriverVehicleService, DriverVehicleRepository],
})
export class DriverVehicleModule {}
