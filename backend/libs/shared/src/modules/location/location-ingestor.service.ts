import { Injectable, Logger } from '@nestjs/common';
import {
  CellDriverRepository,
  DriverMetadata,
  DriverMetadataRepository,
  H3UtilityService,
} from '@shared/shared';

/**
 * Service responsible for processing driver location updates
 * and updating Redis stores for efficient location-based queries
 */
@Injectable()
export class LocationIngestorService {
  private readonly logger = new Logger(LocationIngestorService.name);

  constructor(
    private readonly cellDriverRepository: CellDriverRepository,
    private readonly driverMetadataRepository: DriverMetadataRepository,
    private readonly h3UtilityService: H3UtilityService,
  ) {}

  /**
   * Get drivers in a specific H3 cell
   */
  async getDriversInCell(
    city: string,
    h3Index: string,
    limit: number = 50,
  ): Promise<Array<{ driverId: string; metadata: DriverMetadata | null }>> {
    try {
      const driversWithTimestamps =
        await this.cellDriverRepository.findRecentDriverIds(
          city,
          h3Index,
          limit,
        );

      if (driversWithTimestamps.length === 0) {
        return [];
      }

      const driverIds = driversWithTimestamps.map((d) => d.driverId);
      const metadataMap =
        await this.driverMetadataRepository.getDriversMetadata(driverIds);

      return driversWithTimestamps.map(({ driverId }) => ({
        driverId,
        metadata: metadataMap[driverId],
      }));
    } catch (error) {
      this.logger.error(
        `Failed to get drivers in cell ${h3Index} for city ${city}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Find all nearby drivers within a specified distance from an H3 cell
   * @param h3Cell - Center H3 cell
   * @param distance - Search distance in kilometers (used to determine H3 ring size)
   * @param city - City to search in
   * @param limit - Maximum number of drivers to return
   * @returns Array of drivers with metadata
   */
  async findAllNearbyDrivers(
    h3Cell: string,
    distance: number,
    city?: string,
    limit: number = 50,
  ): Promise<Array<{ driverId: string; metadata: DriverMetadata | null }>> {
    try {
      this.logger.log(
        `Finding nearby drivers within ${distance} H3 cells of H3 cell ${h3Cell}`,
      );

      // Calculate H3 ring size based on distance
      // Approximate: 1km ≈ H3 ring size 1-2 at resolution 8
      // const ringSize = Math.max(1, Math.ceil(distance / 2));

      // Get neighboring H3 cells within the ring
      const neighboringCells = this.h3UtilityService.getNeighboringCells(
        h3Cell,
        distance,
      );

      // Include the center cell
      const allCells = [h3Cell, ...neighboringCells];

      this.logger.debug(
        `Searching in ${allCells.length} H3 cells (ring size: ${distance})`,
      );

      // Get drivers from all cells
      const allDrivers: Array<{
        driverId: string;
        metadata: DriverMetadata | null;
      }> = [];

      for (const cell of allCells) {
        try {
          // Use city from metadata if not provided
          let searchCity = city;
          if (!searchCity) {
            // Try to get city from first driver's metadata in the cell
            const sampleDrivers = await this.getDriversInCell('', cell, 1);
            if (sampleDrivers.length > 0 && sampleDrivers[0].metadata) {
              searchCity = sampleDrivers[0].metadata.city;
            }
          }

          if (searchCity) {
            const cellDrivers = await this.getDriversInCell(
              searchCity,
              cell,
              limit,
            );
            allDrivers.push(...cellDrivers);
          }
        } catch (error) {
          this.logger.warn(`Failed to get drivers from cell ${cell}:`, error);
          // Continue with other cells
        }
      }

      // Remove duplicates and limit results
      const uniqueDrivers = allDrivers.reduce(
        (acc, driver) => {
          if (!acc.find((d) => d.driverId === driver.driverId)) {
            acc.push(driver);
          }
          return acc;
        },
        [] as Array<{ driverId: string; metadata: DriverMetadata | null }>,
      );

      const limitedDrivers = uniqueDrivers.slice(0, limit);

      this.logger.log(`Found ${limitedDrivers.length} unique nearby drivers`);

      return limitedDrivers;
    } catch (error) {
      this.logger.error(
        `Failed to find nearby drivers for H3 cell ${h3Cell}`,
        error,
      );
      throw error;
    }
  }
}
