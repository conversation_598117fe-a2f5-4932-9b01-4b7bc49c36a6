import { Module } from '@nestjs/common';
import { CityProductService } from './city-product.service';
import { CityProductRepository } from '../../repositories/city-product.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { CityModule } from '../city/city.module';
import { ProductModule } from '../product/product.module';

@Module({
  imports: [CityModule, ProductModule],
  providers: [CityProductService, CityProductRepository, PrismaService],
  exports: [CityProductService, CityProductRepository],
})
export class CityProductModule {}
