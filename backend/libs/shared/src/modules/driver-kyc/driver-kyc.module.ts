import { Module } from '@nestjs/common';
import { DriverKycService } from './driver-kyc.service';
import { DriverKycRepository } from '../../repositories/driver-kyc.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { KycDocumentModule } from '../kyc-document/kyc-document.module';
import { UserProfileModule } from '../user-profile/user-profile.module';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule } from '../../config';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';

@Module({
  imports: [
    KycDocumentModule,
    UserProfileModule,
    AppConfigModule,
    UserOnboardingModule,
  ],
  providers: [
    DriverKycService,
    DriverKycRepository,
    PrismaService,
    FileUploadService,
    UserProfileRepository,
  ],
  exports: [DriverKycService, DriverKycRepository],
})
export class DriverKycModule {}
