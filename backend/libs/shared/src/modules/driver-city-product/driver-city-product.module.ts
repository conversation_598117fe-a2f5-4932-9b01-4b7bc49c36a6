import { Module } from '@nestjs/common';
import { DriverCityProductService } from './driver-city-product.service';
import { DriverCityProductRepository } from '../../repositories/driver-city-product.repository';
import { CityProductRepository } from '../../repositories/city-product.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { PrismaService } from '../../database/prisma/prisma.service';

@Module({
  providers: [
    DriverCityProductService,
    DriverCityProductRepository,
    CityProductRepository,
    UserProfileRepository,
    PrismaService,
  ],
  exports: [DriverCityProductService, DriverCityProductRepository],
})
export class DriverCityProductModule {}
