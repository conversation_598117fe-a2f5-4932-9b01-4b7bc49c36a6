import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DriverCityProductRepository } from '../../repositories/driver-city-product.repository';
import { CityProductRepository } from '../../repositories/city-product.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { DriverCityProduct } from '../../repositories/models/driverCityProduct.model';

@Injectable()
export class DriverCityProductService {
  constructor(
    private readonly driverCityProductRepository: DriverCityProductRepository,
    private readonly cityProductRepository: CityProductRepository,
    private readonly userProfileRepository: UserProfileRepository,
  ) {}

  /**
   * Add city products to a driver.
   * @param driverId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs to add
   */
  async addCityProductsToDriver(
    driverId: string,
    cityProductIds: string[],
  ): Promise<DriverCityProduct[]> {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Validate city products exist and are enabled
    const cityProducts = await this.cityProductRepository.findMany({
      where: {
        id: { in: cityProductIds },
        isEnabled: true,
      },
    });

    if (cityProducts.length !== cityProductIds.length) {
      const foundIds = cityProducts.map((cp) => cp.id);
      const notFoundIds = cityProductIds.filter((id) => !foundIds.includes(id));
      throw new BadRequestException(
        `City products not found or disabled: ${notFoundIds.join(', ')}`,
      );
    }

    // Add city products to driver
    return this.driverCityProductRepository.addCityProductsToDriver(
      driverId,
      cityProductIds,
    );
  }

  /**
   * Remove city products from a driver.
   * @param driverId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs to remove
   */
  async removeCityProductsFromDriver(
    driverId: string,
    cityProductIds: string[],
  ): Promise<{ message: string }> {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Check if driver has these city products
    const existingRelations =
      await this.driverCityProductRepository.findDriverCityProductsByCityProductIds(
        driverId,
        cityProductIds,
      );

    if (existingRelations.length === 0) {
      throw new BadRequestException(
        'No city products found for this driver with the provided IDs',
      );
    }

    // Remove city products from driver
    await this.driverCityProductRepository.removeCityProductsFromDriver(
      driverId,
      cityProductIds,
    );

    return {
      message: `Successfully removed ${existingRelations.length} city products from driver`,
    };
  }

  /**
   * Get paginated list of city products for a driver.
   * @param driverId - Driver's user profile ID
   * @param page - Page number
   * @param limit - Items per page
   * @param productName - Product name search term
   */
  async getDriverCityProducts(
    driverId: string,
    page = 1,
    limit = 10,
    productName?: string,
  ) {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Get paginated city products for driver
    const result =
      await this.driverCityProductRepository.paginateDriverCityProducts(
        driverId,
        page,
        limit,
        productName,
      );

    // Format the response to include signed URLs for product icons
    const formattedData = result.data.map((item: any) => ({
      id: item.id,
      userProfileId: item.userProfileId,
      cityProductId: item.cityProductId,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      cityProduct: {
        id: item.cityProduct.id,
        cityId: item.cityProduct.cityId,
        productId: item.cityProduct.productId,
        vehicleTypeId: item.cityProduct.vehicleTypeId,
        isEnabled: item.cityProduct.isEnabled,
        city: item.cityProduct.city,
        product: {
          ...item.cityProduct.product,
          // Note: Add signed URL logic here if needed for product icons
        },
        vehicleType: item.cityProduct.vehicleType,
      },
      userProfile: item.userProfile,
    }));

    return {
      ...result,
      data: formattedData,
    };
  }

  /**
   * Get all city products for a driver (without pagination).
   * @param driverId - Driver's user profile ID
   */
  async getAllDriverCityProducts(
    driverId: string,
  ): Promise<DriverCityProduct[]> {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    return this.driverCityProductRepository.findDriverCityProductsByDriverId(
      driverId,
    );
  }
  /**
   * Check if driver has specific city products.
   * @param driverId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs to check
   */
  async checkDriverHasCityProducts(
    driverId: string,
    cityProductIds: string[],
  ): Promise<{ [key: string]: boolean }> {
    const result: { [key: string]: boolean } = {};

    for (const cityProductId of cityProductIds) {
      result[cityProductId] =
        await this.driverCityProductRepository.driverCityProductExists(
          driverId,
          cityProductId,
        );
    }

    return result;
  }
}
