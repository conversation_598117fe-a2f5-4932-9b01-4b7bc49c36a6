import { Module } from '@nestjs/common';
import { DriverVehicleDocumentService } from './driver-vehicle-document.service';
import { DriverVehicleDocumentRepository } from '../../repositories/driver-vehicle-document.repository';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { CashfreeVehicleVerificationService } from '@shared/shared/common/verifications/cashfree/cashfree-vehicle-verification.service';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { AppConfigModule } from '@shared/shared/config';
import { VehicleDocumentRepository } from '@shared/shared/repositories/vehicle-document.repository';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { UserProfileModule } from '../user-profile/user-profile.module';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';

@Module({
  imports: [AppConfigModule, UserProfileModule, UserOnboardingModule],
  providers: [
    DriverVehicleDocumentService,
    DriverVehicleDocumentRepository,
    DriverVehicleRepository,
    CashfreeVehicleVerificationService,
    PrismaService,
    VehicleDocumentRepository,
    FileUploadService,
  ],
  exports: [DriverVehicleDocumentService],
})
export class DriverVehicleDocumentModule {}
