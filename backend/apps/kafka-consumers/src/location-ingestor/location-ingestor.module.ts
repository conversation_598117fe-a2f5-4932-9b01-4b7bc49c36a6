import { Module } from '@nestjs/common';
import { LocationIngestorService } from './services/location-ingestor.service';
import { LocationIngestorConsumer } from './location-ingestor.consumer';
import {
  CellDriverRepository,
  DriverCellMappingRepository,
  DriverMetadataRepository,
  H3UtilityService,
  RedisService,
} from '@shared/shared';
@Module({
  providers: [
    LocationIngestorService,
    LocationIngestorConsumer,
    CellDriverRepository,
    DriverCellMappingRepository,
    DriverMetadataRepository,
    H3UtilityService,
    RedisService,
  ],
  exports: [
    LocationIngestorService,
    LocationIngestorConsumer,
    CellDriverRepository,
    DriverCellMappingRepository,
    DriverMetadataRepository,
  ],
})
export class LocationIngestorModule {}
