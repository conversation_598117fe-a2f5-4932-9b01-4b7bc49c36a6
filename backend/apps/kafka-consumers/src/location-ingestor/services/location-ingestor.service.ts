import { Injectable, Logger } from '@nestjs/common';
import {
  CellDriverRepository,
  DriverCellMappingRepository,
  DriverMetadata,
  DriverMetadataRepository,
  H3UtilityService,
} from '@shared/shared';
import { DriverLocationReceivedDto } from '@shared/shared/event-emitter/dto/driver-location-received.dto';

/**
 * Service responsible for processing driver location updates
 * and updating Redis stores for efficient location-based queries
 */
@Injectable()
export class LocationIngestorService {
  private readonly logger = new Logger(LocationIngestorService.name);

  constructor(
    private readonly cellDriverRepository: CellDriverRepository,
    private readonly driverCellMappingRepository: DriverCellMappingRepository,
    private readonly driverMetadataRepository: DriverMetadataRepository,
    private readonly h3UtilityService: H3UtilityService,
  ) {}

  /**
   * Process driver location received event
   */
  async processDriverLocationUpdate(
    data: DriverLocationReceivedDto,
  ): Promise<void> {
    try {
      this.logger.log(`Processing location update for driver ${data.driverId}`);

      // Step 1: Convert lat,lon to H3 index (resolution 8)
      const newH3Index = this.h3UtilityService.coordinatesToH3Index(
        data.lat,
        data.lon,
        8,
      );
      this.logger.debug(`Driver ${data.driverId} new H3 index: ${newH3Index}`);

      // Step 2: Find previous H3 index for this driver
      const previousH3Index =
        await this.driverCellMappingRepository.getDriverCell(data.driverId);
      this.logger.debug(
        `Driver ${data.driverId} previous H3 index: ${previousH3Index}`,
      );

      // Step 3: Update cell assignments if necessary
      const timestamp = new Date(data.timestamp).getTime();

      if (previousH3Index && previousH3Index !== newH3Index) {
        // Remove from previous cell and add to new cell atomically
        await this.cellDriverRepository.moveDriver(
          data.city,
          previousH3Index,
          newH3Index,
          data.driverId,
          timestamp,
        );
      } else if (!previousH3Index) {
        // New driver, just add to the new cell
        await this.cellDriverRepository.addDriver(
          data.city,
          newH3Index,
          data.driverId,
          timestamp,
        );
      } else {
        // Same cell, just update timestamp
        await this.cellDriverRepository.addDriver(
          data.city,
          newH3Index,
          data.driverId,
          timestamp,
        );
      }

      // Step 4: Update driver cell mapping
      await this.driverCellMappingRepository.setDriverCell(
        data.driverId,
        newH3Index,
      );

      // Step 5: Update driver metadata
      const metadata: DriverMetadata = {
        driverId: data.driverId,
        lat: data.lat,
        lon: data.lon,
        h3Index: newH3Index,
        status: data.status,
        timestamp: data.timestamp,
        city: data.city,
        rideId: data.rideId || undefined,
      };

      await this.driverMetadataRepository.setDriverMetadata(metadata);

      this.logger.log(
        `Successfully processed location update for driver ${data.driverId} ` +
          `(${previousH3Index ? `${previousH3Index} -> ` : ''}${newH3Index})`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process location update for driver ${data.driverId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : '',
      );
      throw error;
    }
  }

  /**
   * Get drivers in a specific H3 cell
   */
  async getDriversInCell(
    city: string,
    h3Index: string,
    limit: number = 50,
  ): Promise<Array<{ driverId: string; metadata: DriverMetadata | null }>> {
    try {
      const driversWithTimestamps =
        await this.cellDriverRepository.findRecentDriverIds(
          city,
          h3Index,
          limit,
        );

      if (driversWithTimestamps.length === 0) {
        return [];
      }

      const driverIds = driversWithTimestamps.map((d) => d.driverId);
      const metadataMap =
        await this.driverMetadataRepository.getDriversMetadata(driverIds);

      return driversWithTimestamps.map(({ driverId }) => ({
        driverId,
        metadata: metadataMap[driverId],
      }));
    } catch (error) {
      this.logger.error(
        `Failed to get drivers in cell ${h3Index} for city ${city}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove driver from all location-related stores
   */
  async removeDriver(driverId: string): Promise<void> {
    try {
      // Get current driver cell to remove from
      const currentCell =
        await this.driverCellMappingRepository.getDriverCell(driverId);
      const metadata =
        await this.driverMetadataRepository.getDriverMetadata(driverId);

      if (currentCell && metadata) {
        // Remove from cell
        await this.cellDriverRepository.removeDriver(
          metadata.city,
          currentCell,
          driverId,
        );
      }

      // Remove from mapping and metadata stores
      await Promise.all([
        this.driverCellMappingRepository.removeDriverCell(driverId),
        this.driverMetadataRepository.removeDriverMetadata(driverId),
      ]);

      this.logger.log(`Removed driver ${driverId} from all location stores`);
    } catch (error) {
      this.logger.error(
        `Failed to remove driver ${driverId} from location stores`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get statistics about driver distribution
   */
  async getLocationStats(city: string): Promise<{
    totalDrivers: number;
    cellsWithDrivers: number;
    avgDriversPerCell: number;
  }> {
    try {
      const totalDrivers =
        await this.driverCellMappingRepository.getDriverCellCount();

      // This is a simplified version - you'd need to implement cell scanning
      // to get accurate statistics per city

      return {
        totalDrivers,
        cellsWithDrivers: 0, // TODO: Implement cell scanning
        avgDriversPerCell: 0, // TODO: Calculate average
      };
    } catch (error) {
      this.logger.error(`Failed to get location stats for city ${city}`, error);
      throw error;
    }
  }
}
