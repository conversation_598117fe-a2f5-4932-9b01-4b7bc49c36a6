import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { IEventName } from '@shared/shared';
import { DriverLocationReceivedDto } from '@shared/shared/event-emitter/dto/driver-location-received.dto';
import { LocationIngestorService } from './services/location-ingestor.service';

@Controller()
export class LocationIngestorConsumer {
  private readonly logger = new Logger(LocationIngestorConsumer.name);

  constructor(
    private readonly locationIngestorService: LocationIngestorService,
  ) {}

  @EventPattern(IEventName.DRIVER_LOCATION_RECEIVED)
  async handleDriverLocationReceived(@Payload() message: any) {
    try {
      this.logger.log(
        '[LOCATION INGESTOR] Received DRIVER_LOCATION_RECEIVED event',
      );

      const parsedMessage =
        typeof message.value === 'string'
          ? JSON.parse(message.value)
          : message.value;

      this.logger.debug('Parsed message:', JSON.stringify(parsedMessage));

      const eventData = parsedMessage.data;

      if (!eventData) {
        this.logger.error('No data found in event message');
        return;
      }

      const locationDto = plainToClass(DriverLocationReceivedDto, eventData);
      const validationErrors = await validate(locationDto);

      if (validationErrors.length > 0) {
        this.logger.error(
          'Validation failed for driver location data',
          validationErrors.map((error) => ({
            property: error.property,
            constraints: error.constraints,
          })),
        );
        return;
      }

      await this.locationIngestorService.processDriverLocationUpdate(
        locationDto,
      );

      this.logger.log(
        `[LOCATION INGESTOR] Successfully processed location update for driver ${locationDto.driverId}`,
      );
    } catch (error) {
      this.logger.error(
        '[LOCATION INGESTOR] Error processing DRIVER_LOCATION_RECEIVED event:',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : '',
      );

      // TODO:
      //  In a production system, you might want to:
      // 1. Send to a dead letter queue for manual review
      // 2. Implement retry logic with exponential backoff
      // 3. Alert monitoring systems
    }
  }
}
