import { NestFactory } from '@nestjs/core';
import { KafkaConsumersModule } from './kafka-consumers.module';
import { Logger } from '@nestjs/common';
import { AppConfigService, KafkaServerOptionsFactory } from '@shared/shared';

async function bootstrap() {
  const logger = new Logger('KafkaConsumers');

  const app = await NestFactory.create(KafkaConsumersModule, {
    logger: ['error', 'warn', 'log'],
  });

  const cfg = app.get(AppConfigService);
  const kafkaOptions = app.get(KafkaServerOptionsFactory).create();

  logger.log('Starting Kafka Consumers microservice...');
  logger.log(`Brokers: ${cfg.kafkaBrokers.join(', ')}`);
  logger.log(`ClientId: ${cfg.kafkaClientId}`);
  logger.log(`GroupId: ${cfg.kafkaGroupId}`);

  app.connectMicroservice(kafkaOptions);
  app.enableShutdownHooks(['SIGINT', 'SIGTERM']);

  const forceExitAfter = (ms: number) =>
    setTimeout(() => {
      logger.error(`Forced shutdown after ${ms}ms timeout`);
      process.exit(1);
    }, ms).unref();

  const onSignal = (sig: string) => {
    logger.log(`${sig} received; graceful shutdown started...`);
    const timer = forceExitAfter(cfg.shutdownTimeout);
    app
      .close()
      .then(() => {
        clearTimeout(timer);
        logger.log('Kafka Consumers shut down successfully');
        process.exit(0);
      })
      .catch((err) => {
        clearTimeout(timer);
        logger.error('Error during shutdown', err);
        process.exit(1);
      });
  };

  process.once('SIGINT', () => onSignal('SIGINT'));
  process.once('SIGTERM', () => onSignal('SIGTERM'));
  process.once('uncaughtException', (error) => {
    logger.error('Uncaught Exception', error);
    process.exit(1);
  });
  process.once('unhandledRejection', (reason: any, promise) => {
    logger.error(`Unhandled Rejection at: ${promise}`, reason);
    process.exit(1);
  });

  await app.startAllMicroservices();
  logger.log('Kafka Consumers microservice is listening to topics');
}

bootstrap().catch((e) => {
  // eslint-disable-next-line no-console
  console.error('Bootstrap failed:', e);
  process.exit(1);
});
