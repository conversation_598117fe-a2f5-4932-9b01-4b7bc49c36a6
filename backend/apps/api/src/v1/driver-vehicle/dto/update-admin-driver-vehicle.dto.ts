import { ApiProperty } from '@nestjs/swagger';
import {
  IsUUID,
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { DriverVehicleStatus } from '@shared/shared/repositories/models/driverVehicle.model';

export class UpdateAdminDriverVehicleDto {
  @ApiProperty({
    example: 'vehicle-type-uuid',
    description: 'Vehicle type ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  vehicleTypeId?: string;

  @ApiProperty({
    example: 'MH12AB1234',
    description: 'Vehicle registration number',
    required: false,
  })
  @IsOptional()
  @IsString()
  vehicleNumber?: string;

  @ApiProperty({
    example: 'city-product-uuid',
    description: 'City product ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  cityProductId?: string;

  @ApiProperty({
    example: false,
    description: 'Whether NOC is required for this vehicle',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isNocRequired?: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether this is the primary vehicle for the driver',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;

  @ApiProperty({
    enum: DriverVehicleStatus,
    example: DriverVehicleStatus.active,
    description: 'Vehicle status',
    required: false,
  })
  @IsOptional()
  @IsEnum(DriverVehicleStatus)
  status?: DriverVehicleStatus;
}
