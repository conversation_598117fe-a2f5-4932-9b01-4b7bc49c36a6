import { ApiProperty } from '@nestjs/swagger';

export class CityResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '<PERSON><PERSON>' })
  name!: string;

  @ApiProperty({
    example: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
    required: false,
    nullable: true,
    description: 'Signed URL for the icon (valid for 1 hour)',
  })
  icon?: string | null;

  @ApiProperty({
    example: 'Kerala',
    required: false,
    nullable: true,
  })
  state?: string | null;

  @ApiProperty({
    example: 'India',
    required: false,
    nullable: true,
  })
  country?: string | null;

  @ApiProperty({
    example: 'active',
    enum: ['active', 'inactive'],
  })
  status!: string;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;
}
