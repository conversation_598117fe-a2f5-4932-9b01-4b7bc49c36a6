import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

export class EnableProductDto {
  @ApiProperty({
    example: false,
    description: 'Whether to enable all city products for this product',
    default: false,
    required: false,
  })
  @IsBoolean({ message: 'enableAllCityProducts must be a boolean' })
  @IsOptional()
  enableAllCityProducts?: boolean;
}
