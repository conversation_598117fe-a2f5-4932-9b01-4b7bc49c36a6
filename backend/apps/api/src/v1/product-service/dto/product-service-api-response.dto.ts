import { ApiProperty } from '@nestjs/swagger';
import { ProductServiceResponseDto } from './product-service-response.dto';

export class ProductServiceApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Product service created successfully' })
  message!: string;

  @ApiProperty({ type: ProductServiceResponseDto })
  data!: ProductServiceResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class ProductServiceListApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Product services fetched successfully' })
  message!: string;

  @ApiProperty({
    type: [ProductServiceResponseDto],
    description: 'Array of product services',
  })
  data!: ProductServiceResponseDto[];

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class ProductServiceCreateApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Product service created successfully' })
  message!: string;

  @ApiProperty({
    type: ProductServiceResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'Ride Sharing',
      description: 'On-demand ride sharing service',
      icon: 'https://signed-url.amazonaws.com/uploads/icons/ride-sharing.png',
      identifier: 'ride_sharing',
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: ProductServiceResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class ProductServiceUpdateApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Product service updated successfully' })
  message!: string;

  @ApiProperty({
    type: ProductServiceResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'Updated Ride Sharing',
      description: 'Updated on-demand ride sharing service',
      icon: 'https://signed-url.amazonaws.com/uploads/icons/updated-ride-sharing.png',
      identifier: 'ride_sharing',
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T12:00:00Z',
      deletedAt: null,
    },
  })
  data!: ProductServiceResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class ProductServiceDeleteApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Product service deleted successfully' })
  message!: string;

  @ApiProperty({
    type: ProductServiceResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'Ride Sharing',
      description: 'On-demand ride sharing service',
      icon: 'https://signed-url.amazonaws.com/uploads/icons/ride-sharing.png',
      identifier: 'ride_sharing',
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: '2023-12-01T14:00:00Z',
    },
  })
  data!: ProductServiceResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}
