import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNotEmpty,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ProductServiceLanguageSpecDto {
  @ApiProperty({
    example: {
      en: 'Ride Sharing',
      fr: 'Partage de Trajet',
      ml: 'റൈഡ് ഷെയറിംഗ്',
    },
    description: 'Name translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  name?: { [languageCode: string]: string } | undefined;

  @ApiProperty({
    example: {
      en: 'On-demand ride service',
      fr: 'Service de trajet à la demande',
      ml: 'ആവശ്യാനുസരണം റൈഡ് സേവനം',
    },
    description: 'Description translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  description?: { [languageCode: string]: string } | undefined;

  @ApiProperty({
    example: {
      en: 'Premium Service',
      fr: 'Service Premium',
      ml: 'പ്രീമിയം സേവനം',
    },
    description: 'Title translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  title?: { [languageCode: string]: string } | undefined;

  [key: string]: { [languageCode: string]: string } | undefined;
}

export class CreateProductServiceDto {
  @ApiProperty({
    example: 'Ride Sharing',
    description: 'Name of the product service',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    example: 'On-demand ride sharing service',
    description: 'Description of the product service',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: 'uploads/icons/ride-sharing.png',
    description: 'Icon file path for the product service',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    example: 'ride_sharing',
    description:
      'Unique identifier for the product service (auto-generated if not provided)',
    required: false,
  })
  @IsOptional()
  @IsString()
  identifier?: string;

  @ApiProperty({
    type: ProductServiceLanguageSpecDto,
    description: 'Language specifications for translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductServiceLanguageSpecDto)
  languageSpec?: ProductServiceLanguageSpecDto;
}
