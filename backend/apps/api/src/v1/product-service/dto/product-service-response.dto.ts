import { ApiProperty } from '@nestjs/swagger';

export class ProductServiceLanguageSpecResponseDto {
  @ApiProperty({
    example: {
      en: 'Ride Sharing',
      fr: 'Partage de Trajet',
      ml: 'റൈഡ് ഷെയറിംഗ്',
    },
    description: 'Name translations',
    required: false,
  })
  name?: { [languageCode: string]: string };

  @ApiProperty({
    example: {
      en: 'On-demand ride service',
      fr: 'Service de trajet à la demande',
      ml: 'ആവശ്യാനുസരണം റൈഡ് സേവനം',
    },
    description: 'Description translations',
    required: false,
  })
  description?: { [languageCode: string]: string };

  @ApiProperty({
    example: {
      en: 'Premium Service',
      fr: 'Service Premium',
      ml: 'പ്രീമിയം സേവനം',
    },
    description: 'Title translations',
    required: false,
  })
  title?: { [languageCode: string]: string };
}

export class ProductServiceResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id!: string;

  @ApiProperty({ example: 'Ride Sharing' })
  name!: string;

  @ApiProperty({
    example: 'On-demand ride sharing service',
    required: false,
    nullable: true,
  })
  description?: string | null;

  @ApiProperty({
    example: 'https://signed-url.amazonaws.com/uploads/icons/ride-sharing.png',
    required: false,
    nullable: true,
    description: 'Signed URL for the icon (valid for 1 hour)',
  })
  icon?: string | null;

  @ApiProperty({
    example: 'ride_sharing',
    required: false,
    nullable: true,
  })
  identifier?: string | null;

  @ApiProperty({
    type: ProductServiceLanguageSpecResponseDto,
    description: 'Language specifications for translations',
    required: false,
  })
  languageSpec?: ProductServiceLanguageSpecResponseDto;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;
}
