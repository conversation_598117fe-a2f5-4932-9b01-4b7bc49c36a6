import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ProductServiceService } from '../../../../../libs/shared/src/modules/product-service/product-service.service';
import { CreateProductServiceDto } from './dto/create-product-service.dto';
import { UpdateProductServiceDto } from './dto/update-product-service.dto';
import {
  ProductServiceCreateApiResponseDto,
  ProductServiceListApiResponseDto,
  ProductServiceApiResponseDto,
  ProductServiceUpdateApiResponseDto,
  ProductServiceDeleteApiResponseDto,
} from './dto/product-service-api-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Product Services')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('product-services')
export class ProductServiceController {
  constructor(private readonly productServiceService: ProductServiceService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new product service' })
  @ApiResponse({
    status: 201,
    type: ProductServiceCreateApiResponseDto,
    description: 'Product service created successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async create(@Body() body: CreateProductServiceDto) {
    const data = await this.productServiceService.createProductService(body);
    return {
      success: true,
      message: 'Product service created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all product services' })
  @ApiResponse({
    status: 200,
    type: ProductServiceListApiResponseDto,
    description: 'Product services fetched successfully',
  })
  async findAll() {
    const data = await this.productServiceService.findAllProductServices();
    return {
      success: true,
      message: 'Product services fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product service by ID' })
  @ApiResponse({
    status: 200,
    type: ProductServiceApiResponseDto,
    description: 'Product service fetched successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async findOne(@Param('id') id: string) {
    const data = await this.productServiceService.findProductServiceById(id);
    return {
      success: true,
      message: 'Product service fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a product service' })
  @ApiResponse({
    status: 200,
    type: ProductServiceUpdateApiResponseDto,
    description: 'Product service updated successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async update(@Param('id') id: string, @Body() body: UpdateProductServiceDto) {
    const data = await this.productServiceService.updateProductService(
      id,
      body,
    );
    return {
      success: true,
      message: 'Product service updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a product service (soft delete)' })
  @ApiResponse({
    status: 200,
    type: ProductServiceDeleteApiResponseDto,
    description: 'Product service deleted successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async remove(@Param('id') id: string) {
    const data = await this.productServiceService.deleteProductService(id);
    return {
      success: true,
      message: 'Product service deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id/permanent')
  @ApiOperation({ summary: 'Permanently delete a product service' })
  @ApiResponse({
    status: 200,
    type: ProductServiceDeleteApiResponseDto,
    description: 'Product service permanently deleted successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async permanentRemove(@Param('id') id: string) {
    const data =
      await this.productServiceService.permanentDeleteProductService(id);
    return {
      success: true,
      message: 'Product service permanently deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}
