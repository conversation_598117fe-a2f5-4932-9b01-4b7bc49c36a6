import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON>E<PERSON>,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum DriverStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  BUSY = 'busy',
  IN_RIDE = 'in_ride',
}

export class DriverLocationDto {
  @ApiProperty({
    description: 'Driver ID',
    example: 'driver_123',
  })
  @IsString()
  @IsNotEmpty()
  driverId!: string;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: 12.9716,
  })
  @IsNumber()
  lat!: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 77.5946,
  })
  @IsNumber()
  lon!: number;

  @ApiProperty({
    description: 'Timestamp of location update',
    example: '2023-08-06T09:16:42.000Z',
  })
  @IsString()
  @IsNotEmpty()
  timestamp!: string;

  @ApiProperty({
    description: 'City where driver is located',
    example: 'bangalore',
  })
  @IsString()
  @IsNotEmpty()
  city!: string;

  @ApiProperty({
    description: 'Current driver status',
    enum: DriverStatus,
    example: DriverStatus.ONLINE,
  })
  @IsEnum(DriverStatus)
  status!: DriverStatus;

  @ApiProperty({
    description: 'Ride ID if driver is currently in a ride',
    example: 'ride_456',
    required: false,
  })
  @IsOptional()
  @IsString()
  rideId?: string;
}
