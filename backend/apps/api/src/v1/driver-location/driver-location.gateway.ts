import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { DriverLocationDto } from './dto/driver-location.dto';
import { DriverLocationService } from './driver-location.service';
import { WsJwtAuthGuard, WsTokenManagerService } from '@shared/shared';

@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
  allowEIO3: true,
})
export class DriverLocationGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(DriverLocationGateway.name);
  private connectedDrivers = new Map<string, string>();

  constructor(
    private readonly driverLocationService: DriverLocationService,
    private readonly wsTokenManager: WsTokenManagerService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      this.logger.log(`Client connected: ${client.id}`);

      const driverId = this.extractDriverId(client);

      if (driverId) {
        this.connectedDrivers.set(driverId, client.id);
        this.logger.log(
          `Driver ${driverId} connected with socket ${client.id}`,
        );

        await client.join(`driver:${driverId}`);

        client.emit('connected', {
          message: 'Successfully connected to location service',
          driverId,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}:`, error);
      client.disconnect(true);
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      this.logger.log(`Client disconnected: ${client.id}`);

      // Handle token manager cleanup
      this.wsTokenManager.handleSocketDisconnect(client);

      const driverId = Array.from(this.connectedDrivers.entries()).find(
        ([_, socketId]) => socketId === client.id,
      )?.[0];

      if (driverId) {
        this.connectedDrivers.delete(driverId);
        this.logger.log(`Driver ${driverId} disconnected`);

        await this.handleDriverDisconnect(driverId);
      }
    } catch (error) {
      this.logger.error(`Disconnect error for client ${client.id}:`, error);
    }
  }

  @SubscribeMessage('location_update')
  @UseGuards(WsJwtAuthGuard)
  async handleLocationUpdate(
    @MessageBody() data: DriverLocationDto,
    @ConnectedSocket() client: Socket,
  ) {
    console.log('Hello Iam hitting here');
    try {
      const user = (client as any).user;
      const driverId = user?.profileId;

      const locationData = { ...data, driverId };

      this.logger.log(`Received location update from driver ${driverId}`);

      this.driverLocationService.processLocationUpdate(locationData);

      client.emit('location_ack', {
        success: true,
        message: 'Location updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to process location update from driver ${data.driverId}:`,
        error,
      );

      client.emit('location_error', {
        success: false,
        message: 'Failed to update location',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('token_refresh')
  async handleTokenRefresh(
    @MessageBody() data: { token: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const success = await this.wsTokenManager.refreshSocketToken(
        client,
        data.token,
      );

      if (success) {
        client.emit('token_refresh_success', {
          success: true,
          message: 'Token refreshed successfully',
          timestamp: new Date().toISOString(),
        });
        this.logger.log(`Token refreshed for client ${client.id}`);
      } else {
        client.emit('token_refresh_error', {
          success: false,
          message: 'Token refresh failed',
          timestamp: new Date().toISOString(),
        });
        this.logger.warn(`Token refresh failed for client ${client.id}`);
      }
    } catch (error) {
      this.logger.error(`Token refresh error for client ${client.id}:`, error);
      client.emit('token_refresh_error', {
        success: false,
        message: 'Token refresh failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Extract driver ID from socket client
   * Now uses profileId from JWT token as the primary method
   */
  private extractDriverId(client: Socket): string | null {
    try {
      // Method 1: From JWT token (profileId)
      const user = (client as any).user;
      if (user?.profileId) {
        return user.profileId;
      }
      return null;
    } catch (error) {
      this.logger.error('Failed to extract driver ID:', error);
      return null;
    }
  }

  /**
   * Handle driver disconnect logic
   */
  private async handleDriverDisconnect(driverId: string) {
    try {
      // Optional: Update driver status to offline
      // await this.driverLocationService.updateDriverStatus(driverId, 'offline');
      this.logger.log(`Processed disconnect for driver ${driverId}`);
    } catch (error) {
      this.logger.error(
        `Failed to handle disconnect for driver ${driverId}:`,
        error,
      );
    }
  }
}
