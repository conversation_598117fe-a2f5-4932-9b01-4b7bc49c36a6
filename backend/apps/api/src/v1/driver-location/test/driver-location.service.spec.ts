import { Test, TestingModule } from '@nestjs/testing';
import { DriverLocationService } from '../driver-location.service';
import { GlobalEventEmitterService, IEventName } from '@shared/shared';
import { DriverLocationDto, DriverStatus } from '../dto/driver-location.dto';

describe('DriverLocationService', () => {
  let service: DriverLocationService;
  let mockEventEmitter: jest.Mocked<GlobalEventEmitterService>;

  beforeEach(async () => {
    const mockEventEmitterService = {
      emit: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DriverLocationService,
        {
          provide: GlobalEventEmitterService,
          useValue: mockEventEmitterService,
        },
      ],
    }).compile();

    service = module.get<DriverLocationService>(DriverLocationService);
    mockEventEmitter = module.get(GlobalEventEmitterService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processLocationUpdate', () => {
    it('should process location update successfully', async () => {
      const locationData: DriverLocationDto = {
        driverId: 'driver_123',
        lat: 12.9716,
        lon: 77.5946,
        timestamp: '2023-08-06T09:16:42.000Z',
        city: 'bangalore',
        status: DriverStatus.ONLINE,
      };

      mockEventEmitter.emit.mockResolvedValue(undefined);

      await expect(
        service.processLocationUpdate(locationData),
      ).resolves.not.toThrow();

      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        IEventName.DRIVER_LOCATION_RECEIVED,
        {
          driverId: locationData.driverId,
          lat: locationData.lat,
          lon: locationData.lon,
          timestamp: locationData.timestamp,
          city: locationData.city,
          status: locationData.status,
          rideId: locationData.rideId,
        },
      );
    });

    it('should set timestamp if not provided', async () => {
      const locationData: DriverLocationDto = {
        driverId: 'driver_123',
        lat: 12.9716,
        lon: 77.5946,
        timestamp: '',
        city: 'bangalore',
        status: DriverStatus.ONLINE,
      };

      mockEventEmitter.emit.mockResolvedValue(undefined);

      await service.processLocationUpdate(locationData);

      expect(locationData.timestamp).toBeTruthy();
      expect(new Date(locationData.timestamp).getTime()).toBeCloseTo(
        Date.now(),
        -3,
      );
    });

    it('should handle errors properly', async () => {
      const locationData: DriverLocationDto = {
        driverId: 'driver_123',
        lat: 12.9716,
        lon: 77.5946,
        timestamp: '2023-08-06T09:16:42.000Z',
        city: 'bangalore',
        status: DriverStatus.ONLINE,
      };

      const error = new Error('Event emission failed');
      mockEventEmitter.emit.mockRejectedValue(error);

      await expect(service.processLocationUpdate(locationData)).rejects.toThrow(
        'Event emission failed',
      );
    });
  });

  describe('processBatchLocationUpdates', () => {
    it('should process batch updates and return results', async () => {
      const locations: DriverLocationDto[] = [
        {
          driverId: 'driver_1',
          lat: 12.9716,
          lon: 77.5946,
          timestamp: '2023-08-06T09:16:42.000Z',
          city: 'bangalore',
          status: DriverStatus.ONLINE,
        },
        {
          driverId: 'driver_2',
          lat: 13.0827,
          lon: 80.2707,
          timestamp: '2023-08-06T09:17:42.000Z',
          city: 'chennai',
          status: DriverStatus.BUSY,
        },
      ];

      mockEventEmitter.emit.mockResolvedValueOnce(undefined);
      mockEventEmitter.emit.mockRejectedValueOnce(
        new Error('Second emission failed'),
      );

      const results = await service.processBatchLocationUpdates(locations);

      expect(results).toEqual({
        success: 1,
        failed: 1,
        errors: [
          {
            driverId: 'driver_2',
            error: 'Second emission failed',
          },
        ],
      });
    });

    it('should handle empty batch', async () => {
      const results = await service.processBatchLocationUpdates([]);

      expect(results).toEqual({
        success: 0,
        failed: 0,
        errors: [],
      });
    });
  });
});
