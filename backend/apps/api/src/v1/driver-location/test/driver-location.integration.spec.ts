import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { Socket as ClientSocket, io } from 'socket.io-client';
import { DriverLocationGateway } from '../driver-location.gateway';
import { DriverLocationService } from '../driver-location.service';
import { DriverLocationModule } from '../driver-location.module';
import { GlobalEventEmitterService } from '@shared/shared';
import { DriverStatus } from '../dto/driver-location.dto';

describe('DriverLocationGateway (Integration)', () => {
  let app: INestApplication;
  let clientSocket: ClientSocket;
  let mockEventEmitter: jest.Mocked<GlobalEventEmitterService>;

  beforeEach(async () => {
    const mockEventEmitterService = {
      emit: jest.fn(),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [DriverLocationModule],
    })
      .overrideProvider(GlobalEventEmitterService)
      .useValue(mockEventEmitterService)
      .compile();

    app = moduleFixture.createNestApplication();
    mockEventEmitter = moduleFixture.get(GlobalEventEmitterService);

    await app.init();
    await app.listen(3001); // Use a test port

    // Create client socket
    clientSocket = io('http://localhost:3001', {
      query: {
        driverId: 'test_driver_123',
      },
      transports: ['websocket'],
      forceNew: true,
    });
  });

  afterEach(async () => {
    if (clientSocket) {
      clientSocket.disconnect();
    }
    await app.close();
  });

  it('should handle location updates via WebSocket', (done) => {
    mockEventEmitter.emit.mockResolvedValue(undefined);

    // Wait for connection
    clientSocket.on('connected', (data) => {
      expect(data.driverId).toBe('test_driver_123');
      expect(data.message).toBe('Successfully connected to location service');

      // Send location update
      clientSocket.emit('location_update', {
        driverId: 'test_driver_123',
        lat: 12.9716,
        lon: 77.5946,
        timestamp: new Date().toISOString(),
        city: 'bangalore',
        status: DriverStatus.ONLINE,
      });
    });

    // Wait for acknowledgment
    clientSocket.on('location_ack', (response) => {
      expect(response.success).toBe(true);
      expect(response.message).toBe('Location updated successfully');
      expect(mockEventEmitter.emit).toHaveBeenCalled();
      done();
    });

    // Handle errors
    clientSocket.on('location_error', (error) => {
      done(new Error(`Location update failed: ${error.message}`));
    });

    clientSocket.on('connect_error', (error) => {
      done(new Error(`Connection failed: ${error.message}`));
    });
  });

  it('should handle ping/pong messages', (done) => {
    clientSocket.on('connect', () => {
      clientSocket.emit('ping');
    });

    clientSocket.on('pong', (response) => {
      expect(response.timestamp).toBeDefined();
      expect(new Date(response.timestamp).getTime()).toBeCloseTo(
        Date.now(),
        -1000,
      );
      done();
    });

    clientSocket.on('connect_error', (error) => {
      done(new Error(`Connection failed: ${error.message}`));
    });
  });

  it('should handle ride subscription', (done) => {
    let subscriptionReceived = false;

    clientSocket.on('connect', () => {
      clientSocket.emit('subscribe_ride', {
        rideId: 'test_ride_456',
        userId: 'test_user_789',
      });
    });

    clientSocket.on('subscription_ack', (response) => {
      expect(response.success).toBe(true);
      expect(response.rideId).toBe('test_ride_456');
      subscriptionReceived = true;

      // Now simulate a driver location update for a ride
      const secondSocket = io('http://localhost:3001', {
        query: { driverId: 'another_driver' },
        transports: ['websocket'],
        forceNew: true,
      });

      secondSocket.on('connect', () => {
        secondSocket.emit('location_update', {
          driverId: 'another_driver',
          lat: 13.0827,
          lon: 80.2707,
          timestamp: new Date().toISOString(),
          city: 'chennai',
          status: DriverStatus.IN_RIDE,
          rideId: 'test_ride_456',
        });
      });

      secondSocket.disconnect();
    });

    // Listen for driver location broadcast
    clientSocket.on('driver_location', (locationData) => {
      if (subscriptionReceived) {
        expect(locationData.driverId).toBe('another_driver');
        expect(locationData.lat).toBe(13.0827);
        expect(locationData.lon).toBe(80.2707);
        done();
      }
    });

    clientSocket.on('connect_error', (error) => {
      done(new Error(`Connection failed: ${error.message}`));
    });
  });
});
