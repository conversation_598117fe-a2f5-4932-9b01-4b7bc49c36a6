import { Injectable, Logger } from '@nestjs/common';
import { GlobalEventEmitterService, IEventName } from '@shared/shared';
import { DriverLocationDto } from './dto/driver-location.dto';

@Injectable()
export class DriverLocationService {
  private readonly logger = new Logger(DriverLocationService.name);

  constructor(private readonly globalEventEmitter: GlobalEventEmitterService) {}

  /**
   * Process driver location update
   * Validates data, acknowledges receipt, and emits to Kafka for processing
   */
  async processLocationUpdate(locationData: DriverLocationDto): Promise<void> {
    try {
      this.logger.log(
        `Processing location update for driver ${locationData.driverId}`,
      );

      if (!locationData.timestamp) {
        locationData.timestamp = new Date().toISOString();
      }

      await this.globalEventEmitter.emit(IEventName.DRIVER_LOCATION_RECEIVED, {
        driverId: locationData.driverId,
        lat: locationData.lat,
        lon: locationData.lon,
        timestamp: locationData.timestamp,
        city: locationData.city,
        status: locationData.status,
        rideId: locationData.rideId,
      });

      this.logger.log(
        `Successfully emitted location event for driver ${locationData.driverId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process location update for driver ${locationData.driverId}:`,
        error,
      );
      throw error;
    }
  }
}
