import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { DriverLocationGateway } from './driver-location.gateway';
import { DriverLocationService } from './driver-location.service';
import { GlobalEventEmitterModule } from '@shared/shared/event-emitter/event-emitter.module';
import { WsJwtAuthGuard, WsTokenManagerService } from '@shared/shared';
import { AppConfigModule } from '@shared/shared/config';

@Module({
  imports: [
    GlobalEventEmitterModule,
    AppConfigModule,
    JwtModule.register({}), // JWT module for token verification
  ],
  providers: [
    DriverLocationGateway,
    DriverLocationService,
    WsJwtAuthGuard,
    WsTokenManagerService,
  ],
  exports: [DriverLocationGateway, DriverLocationService],
})
export class DriverLocationModule {}
