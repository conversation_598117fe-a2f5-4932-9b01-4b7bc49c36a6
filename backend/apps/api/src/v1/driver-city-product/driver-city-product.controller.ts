import {
  Controller,
  Post,
  Delete,
  Get,
  Param,
  Body,
  Query,
  ParseUUIDPipe,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DriverCityProductService } from '../../../../../libs/shared/src/modules/driver-city-product/driver-city-product.service';
import { AddCityProductsToDriverDto } from './dto/add-city-products.dto';
import { RemoveCityProductsFromDriverDto } from './dto/remove-city-products.dto';
import {
  DriverCityProductListResponseDto,
  AddCityProductsResponseDto,
  RemoveCityProductsResponseDto,
} from './dto/driver-city-product-response.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';

@ApiTags('Driver City Products')
@Controller('driver-city-products')
export class DriverCityProductController {
  constructor(
    private readonly driverCityProductService: DriverCityProductService,
  ) {}

  @Post('drivers/:driverId/add-products')
  @ApiOperation({
    summary: 'Add city products to a driver',
    description: 'Add multiple city products to a specific driver',
  })
  @ApiParam({
    name: 'driverId',
    description: 'Driver user profile ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 201,
    description: 'City products added successfully',
    type: ApiResponseDto<AddCityProductsResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or city products not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver not found',
    type: ApiErrorResponseDto,
  })
  async addCityProductsToDriver(
    @Param('driverId', ParseUUIDPipe) driverId: string,
    @Body() dto: AddCityProductsToDriverDto,
  ) {
    const result = await this.driverCityProductService.addCityProductsToDriver(
      driverId,
      dto.cityProductIds,
    );

    return {
      success: true,
      message: 'City products added successfully to driver',
      data: result,
    };
  }

  @Delete('drivers/:driverId/remove-products')
  @ApiOperation({
    summary: 'Remove city products from a driver',
    description: 'Remove multiple city products from a specific driver',
  })
  @ApiParam({
    name: 'driverId',
    description: 'Driver user profile ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'City products removed successfully',
    type: ApiResponseDto<RemoveCityProductsResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No city products found for removal',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver not found',
    type: ApiErrorResponseDto,
  })
  async removeCityProductsFromDriver(
    @Param('driverId', ParseUUIDPipe) driverId: string,
    @Body() dto: RemoveCityProductsFromDriverDto,
  ) {
    const result =
      await this.driverCityProductService.removeCityProductsFromDriver(
        driverId,
        dto.cityProductIds,
      );

    return {
      success: true,
      message: result.message,
      data: null,
    };
  }

  @Get('drivers/:driverId/products')
  @ApiOperation({
    summary: 'Get city products for a driver',
    description:
      'Get paginated list of city products assigned to a specific driver with optional product name search',
  })
  @ApiParam({
    name: 'driverId',
    description: 'Driver user profile ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'productName',
    required: false,
    description: 'Product name search term',
    example: 'taxi',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver city products retrieved successfully',
    type: ApiResponseDto<DriverCityProductListResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver not found',
    type: ApiErrorResponseDto,
  })
  async getDriverCityProducts(
    @Param('driverId', ParseUUIDPipe) driverId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('productName') productName?: string,
  ) {
    const result = await this.driverCityProductService.getDriverCityProducts(
      driverId,
      page,
      limit,
      productName,
    );

    return {
      success: true,
      message: 'Driver city products retrieved successfully',
      data: result,
    };
  }
}
