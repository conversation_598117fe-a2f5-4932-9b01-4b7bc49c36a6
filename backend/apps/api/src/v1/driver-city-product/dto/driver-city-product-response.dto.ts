import { ApiProperty } from '@nestjs/swagger';

export class CityResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'Kochi' })
  name!: string;

  @ApiProperty({ example: 'Kerala', required: false })
  state?: string;

  @ApiProperty({ example: 'India', required: false })
  country?: string;
}

export class ProductResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'Taxi Service' })
  name!: string;

  @ApiProperty({ example: 'Standard taxi service', required: false })
  description?: string;

  @ApiProperty({
    example: 'https://signed-url.amazonaws.com/products/taxi-icon.png',
    required: false,
    description: 'Signed URL for the product icon (valid for 1 hour)',
  })
  icon?: string;
}

export class VehicleTypeResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'Car' })
  name!: string;

  @ApiProperty({ example: 'Four-wheeler vehicle', required: false })
  description?: string;
}

export class CityProductDetailResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  cityId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  productId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  vehicleTypeId!: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;

  @ApiProperty({ type: CityResponseDto })
  city!: CityResponseDto;

  @ApiProperty({ type: ProductResponseDto })
  product!: ProductResponseDto;

  @ApiProperty({ type: VehicleTypeResponseDto })
  vehicleType!: VehicleTypeResponseDto;
}

export class UserProfileResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440004' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;
}

export class DriverCityProductResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440005' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440004' })
  userProfileId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  cityProductId!: string;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ type: CityProductDetailResponseDto })
  cityProduct!: CityProductDetailResponseDto;

  @ApiProperty({ type: UserProfileResponseDto })
  userProfile!: UserProfileResponseDto;
}

export class DriverCityProductListResponseDto {
  @ApiProperty({ type: [DriverCityProductResponseDto] })
  data!: DriverCityProductResponseDto[];

  @ApiProperty({ example: 1 })
  page!: number;

  @ApiProperty({ example: 10 })
  limit!: number;

  @ApiProperty({ example: 25 })
  total!: number;

  @ApiProperty({ example: 3 })
  totalPages!: number;

  @ApiProperty({ example: true })
  hasNextPage!: boolean;

  @ApiProperty({ example: false })
  hasPrevPage!: boolean;
}

export class AddCityProductsResponseDto {
  @ApiProperty({ type: [DriverCityProductResponseDto] })
  data!: DriverCityProductResponseDto[];

  @ApiProperty({ example: 'City products added successfully to driver' })
  message!: string;
}

export class RemoveCityProductsResponseDto {
  @ApiProperty({ example: 'Successfully removed 2 city products from driver' })
  message!: string;
}
