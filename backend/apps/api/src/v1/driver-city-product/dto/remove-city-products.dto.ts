import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayNotEmpty } from 'class-validator';

export class RemoveCityProductsFromDriverDto {
  @ApiProperty({
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
    description: 'Array of city product IDs to remove from the driver',
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  cityProductIds!: string[];
}
