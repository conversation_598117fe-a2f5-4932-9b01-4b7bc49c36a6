import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import {
  RideSearchService,
  SearchRideParams,
  RideBookingType,
} from '@shared/shared/modules/ride-search/ride-search.service';
import { SearchRideDto } from './dto/search-ride.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Ride Search')
@Controller('rides')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RideSearchController {
  constructor(private readonly rideSearchService: RideSearchService) {}

  @Post('search')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Search for available rides',
    description:
      'Search for available ride options based on pickup and destination locations. Determines service type (city, rental, intercity) based on zone analysis.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ride search completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Ride search completed successfully',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid search parameters',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Pickup time is required when type is later',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Unauthorized' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async searchRides(@Body() searchDto: SearchRideDto) {
    // Prepare search parameters
    const searchParams: SearchRideParams = {
      pickup: {
        lat: searchDto.pickup.lat,
        lng: searchDto.pickup.lng,
      },
      destination: {
        lat: searchDto.destination.lat,
        lng: searchDto.destination.lng,
      },
      type: searchDto.type ? (searchDto.type as unknown as RideBookingType) : RideBookingType.NOW,
      pickupTime: searchDto.pickupTime,
    };

    // Execute search logic
    const rideOptions = await this.rideSearchService.searchRides(searchParams);

    return {
      success: true,
      message: 'Ride search completed successfully',
      data: rideOptions,
      timestamp: Date.now(),
    };
  }
}
