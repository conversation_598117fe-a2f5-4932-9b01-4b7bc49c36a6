import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ProductToAddDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Product ID to add to the city',
  })
  @IsUUID(4, { message: 'Product ID must be a valid UUID' })
  productId!: string;

  @ApiProperty({
    example: 'v1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Vehicle type ID for this product in the city',
  })
  @IsUUID(4, { message: 'Vehicle type ID must be a valid UUID' })
  vehicleTypeId!: string;
}

export class AddProductsToCityDto {
  @ApiProperty({
    type: [ProductToAddDto],
    description: 'Array of products to add to the city',
    example: [
      {
        productId: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
        vehicleTypeId: 'v1a2b3c4-d5e6-7890-abcd-ef1234567890',
      },
      {
        productId: 'c2a2b3c4-d5e6-7890-abcd-ef1234567891',
        vehicleTypeId: 'v2a2b3c4-d5e6-7890-abcd-ef1234567891',
      },
    ],
  })
  @IsArray({ message: 'Products must be an array' })
  @ValidateNested({ each: true })
  @Type(() => ProductToAddDto)
  products!: ProductToAddDto[];
}
