import { ApiProperty } from '@nestjs/swagger';

export class CityResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'Koch<PERSON>' })
  name!: string;

  @ApiProperty({ example: 'Kerala' })
  state?: string;

  @ApiProperty({ example: 'India' })
  country?: string;

  @ApiProperty({ example: 'active' })
  status!: string;
}

export class ProductResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'Standard Ride' })
  name!: string;

  @ApiProperty({ example: 'Basic transportation service' })
  description?: string;

  @ApiProperty({ example: 'standard_ride' })
  identifier?: string;

  @ApiProperty({
    example:
      'https://signed-url.amazonaws.com/uploads/products/standard-ride-icon.png',
    required: false,
    description: 'Signed URL for the icon (valid for 1 hour)',
  })
  icon?: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;
}

export class VehicleTypeResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'Car' })
  name!: string;

  @ApiProperty({ example: 'Four-wheeler vehicle' })
  description?: string;

  @ApiProperty({
    example:
      'https://signed-url.amazonaws.com/uploads/vehicle-types/car-icon.png',
    required: false,
    description: 'Signed URL for the image (valid for 1 hour)',
  })
  image?: string;
}

export class CityProductResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440001' })
  cityId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440002' })
  productId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440003' })
  vehicleTypeId!: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;

  @ApiProperty({ type: CityResponseDto, required: false })
  city?: CityResponseDto;

  @ApiProperty({ type: ProductResponseDto, required: false })
  product?: ProductResponseDto;

  @ApiProperty({ type: VehicleTypeResponseDto, required: false })
  vehicleType?: VehicleTypeResponseDto;
}

// API Response DTOs
export class CityProductAddApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Products added to city successfully' })
  message!: string;

  @ApiProperty({
    type: [CityProductResponseDto],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        cityId: '550e8400-e29b-41d4-a716-446655440001',
        productId: '550e8400-e29b-41d4-a716-446655440002',
        vehicleTypeId: '550e8400-e29b-41d4-a716-446655440003',
        isEnabled: false,
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        deletedAt: null,
      },
    ],
  })
  data!: CityProductResponseDto[];

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityProductRemoveApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Products removed from city successfully' })
  message!: string;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityProductEnableApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City product enabled successfully' })
  message!: string;

  @ApiProperty({
    type: CityProductResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      cityId: '550e8400-e29b-41d4-a716-446655440001',
      productId: '550e8400-e29b-41d4-a716-446655440002',
      vehicleTypeId: '550e8400-e29b-41d4-a716-446655440003',
      isEnabled: true,
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: CityProductResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityProductDisableApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City product disabled successfully' })
  message!: string;

  @ApiProperty({
    type: CityProductResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      cityId: '550e8400-e29b-41d4-a716-446655440001',
      productId: '550e8400-e29b-41d4-a716-446655440002',
      vehicleTypeId: '550e8400-e29b-41d4-a716-446655440003',
      isEnabled: false,
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: CityProductResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityProductPaginatedApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City products retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [CityProductResponseDto],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        cityId: '550e8400-e29b-41d4-a716-446655440001',
        productId: '550e8400-e29b-41d4-a716-446655440002',
        vehicleTypeId: '550e8400-e29b-41d4-a716-446655440003',
        isEnabled: true,
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        deletedAt: null,
      },
    ],
  })
  data!: CityProductResponseDto[];

  @ApiProperty({
    example: {
      page: 1,
      limit: 10,
      total: 25,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: false,
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityProductBulkUpdateApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'All city products updated successfully' })
  message!: string;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}
