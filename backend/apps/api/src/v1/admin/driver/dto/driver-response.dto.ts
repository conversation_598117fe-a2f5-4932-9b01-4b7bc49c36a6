import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@shared/shared/repositories/models/userProfile.model';
import { DriverStatus } from './change-driver-status.dto';

export class DriverResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Driver profile ID',
  })
  id!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'User ID',
  })
  userId!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Role ID',
  })
  roleId!: string;

  @ApiPropertyOptional({
    example: 'John',
    description: 'Driver first name',
  })
  firstName?: string;

  @ApiPropertyOptional({
    example: 'Doe',
    description: 'Driver last name',
  })
  lastName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Driver email address',
  })
  email?: string;

  @ApiPropertyOptional({
    example: '+919876543210',
    description: 'Driver mobile number',
  })
  phoneNumber?: string;

  @ApiPropertyOptional({
    enum: Gender,
    example: Gender.MALE,
    description: 'Driver gender',
  })
  gender?: Gender;

  @ApiPropertyOptional({
    example: '1990-01-15T00:00:00.000Z',
    description: 'Driver date of birth',
  })
  dob?: Date;

  @ApiPropertyOptional({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL',
  })
  profilePictureUrl?: string;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'City ID',
  })
  cityId?: string;

  @ApiPropertyOptional({
    example: 'Mumbai',
    description: 'City name',
  })
  cityName?: string;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Language ID',
  })
  languageId?: string;

  @ApiPropertyOptional({
    example: 'English',
    description: 'Language name',
  })
  languageName?: string;

  @ApiPropertyOptional({
    example: 'ABC123',
    description: 'Referral code',
  })
  referralCode?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether phone number is verified',
  })
  phoneVerified?: boolean;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether email is verified',
  })
  emailVerified?: boolean;

  @ApiProperty({
    enum: DriverStatus,
    example: DriverStatus.ACTIVE,
    description: 'Driver profile status',
  })
  status!: DriverStatus;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Driver creation date',
  })
  createdAt!: Date;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Driver last update date',
  })
  updatedAt!: Date;

  @ApiPropertyOptional({
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      currentStep: 'PROFILE_SETUP',
      lastActiveAt: '2024-01-15T10:30:00.000Z',
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z',
    },
    description: 'Driver onboarding information',
  })
  onboarding?: {
    id: string;
    currentStep: string;
    lastActiveAt?: Date;
    createdAt: Date;
    updatedAt: Date;
  };
}

export default DriverResponseDto;
