import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class LanguageSpecFieldDto {
  [languageCode: string]: string;
}

export class VehicleTypeLanguageSpecDto {
  @ApiProperty({
    type: LanguageSpecFieldDto,
    example: { en: 'Car', fr: 'Voiture', ml: 'കാർ' },
    description: 'Name translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  name?: { [languageCode: string]: string } | undefined;

  @ApiProperty({
    type: LanguageSpecFieldDto,
    example: {
      en: 'Four-wheeler vehicle',
      fr: 'Véhicule à quatre roues',
      ml: 'നാല് ചക്ര വാഹനം',
    },
    description: 'Description translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  description?: { [languageCode: string]: string } | undefined;

  @ApiProperty({
    type: LanguageSpecFieldDto,
    example: { en: 'Sedan', fr: 'Berline', ml: 'സെഡാൻ' },
    description: 'Title translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  title?: { [languageCode: string]: string } | undefined;

  [key: string]: { [languageCode: string]: string } | undefined;
}

export class CreateVehicleDto {
  @ApiProperty({ example: 'Car', description: 'Name of the vehicle type' })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({ example: 'Four-wheeler vehicle', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'https://example.com/image.png', required: false })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({
    type: VehicleTypeLanguageSpecDto,
    description: 'Language specifications for translations',
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => VehicleTypeLanguageSpecDto)
  languageSpec?: VehicleTypeLanguageSpecDto;
}

export class UpdateVehicleDto extends PartialType(CreateVehicleDto) {}
