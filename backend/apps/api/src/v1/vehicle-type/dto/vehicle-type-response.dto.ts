import { ApiProperty } from '@nestjs/swagger';

export class LanguageSpecDto {
  [languageCode: string]: string;
}

export class VehicleTypeLanguageSpecDto {
  @ApiProperty({
    example: {
      en: 'Car',
      fr: 'Voiture',
      ml: 'കാർ',
    },
    description: 'Title translations',
    required: false,
  })
  title?: { [languageCode: string]: string };

  @ApiProperty({
    example: {
      en: 'Four-wheeler vehicle',
      fr: 'Véhicule à quatre roues',
      ml: 'നാല് ചക്ര വാഹനം',
    },
    description: 'Description translations',
    required: false,
  })
  description?: { [languageCode: string]: string };

  @ApiProperty({
    example: {
      en: 'Car',
      fr: 'Voiture',
      ml: 'കാർ',
    },
    description: 'Name translations',
    required: false,
  })
  name?: { [languageCode: string]: string };
}

export class VehicleTypeResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id!: string;

  @ApiProperty({
    example: 'Car',
    description:
      'Vehicle type name (translated if Language-Code header provided)',
  })
  name!: string;

  @ApiProperty({
    example: 'Four-wheeler vehicle',
    description:
      'Vehicle type description (translated if Language-Code header provided)',
    required: false,
  })
  description?: string;

  @ApiProperty({
    example:
      'https://signed-url.amazonaws.com/uploads/vehicle-types/car-icon.png',
    required: false,
    description: 'Signed URL for the image (valid for 1 hour)',
  })
  image?: string;

  @ApiProperty({
    type: VehicleTypeLanguageSpecDto,
    description: 'Language specifications for translations',
    required: false,
  })
  languageSpec?: VehicleTypeLanguageSpecDto;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  updatedAt!: Date;
}

export class VehicleTypeListResponseDto {
  @ApiProperty({ type: [VehicleTypeResponseDto] })
  data!: VehicleTypeResponseDto[];
}

export class TranslatedVehicleTypeExampleDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id!: string;

  @ApiProperty({
    example: 'കാർ',
    description: 'Translated vehicle type name (when Language-Code: ml)',
  })
  name!: string;

  @ApiProperty({
    example: 'നാല് ചക്ര വാഹനം',
    description: 'Translated vehicle type description (when Language-Code: ml)',
  })
  description?: string;

  @ApiProperty({
    example:
      'https://signed-url.amazonaws.com/uploads/vehicle-types/car-icon.png',
    required: false,
  })
  image?: string;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  updatedAt!: Date;
}
