import { Test, TestingModule } from '@nestjs/testing';
import { RabbitmqConsumerController } from './rabbitmq-consumer.controller';
import { RabbitmqConsumerService } from './rabbitmq-consumer.service';

describe('RabbitmqConsumerController', () => {
  let rabbitmqConsumerController: RabbitmqConsumerController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [RabbitmqConsumerController],
      providers: [RabbitmqConsumerService],
    }).compile();

    rabbitmqConsumerController = app.get<RabbitmqConsumerController>(
      RabbitmqConsumerController,
    );
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(rabbitmqConsumerController.getHello()).toBe('Hello World!');
    });
  });
});
