model Zone {
  id        String    @id @default(uuid()) @map("id") @db.Uuid
  name      String    @map("name")
  isCity    Boolean   @default(false) @map("is_city")
  polygon   <PERSON>son      @map("polygon")
  h3Indexes String[]  @map("h3_indexes")
  cityId    String?   @map("city_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  h3IndexToZones H3IndexToZone[]

  @@index([name], name: "idx_zone_name")
  @@index([isCity], name: "idx_zone_is_city")
  @@index([cityId], name: "idx_zone_city_id")
  @@index([h3Indexes], name: "idx_zone_h3_indexes")
  @@map("zones")
}
