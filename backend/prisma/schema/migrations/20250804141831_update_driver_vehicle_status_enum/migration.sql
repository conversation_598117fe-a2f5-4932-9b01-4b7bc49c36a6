-- CreateEnum
CREATE TYPE "UserProfileStatus" AS ENUM ('active', 'pending', 'disabled', 'inactive');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "DriverVehicleStatus" ADD VALUE 'active';
ALTER TYPE "DriverVehicleStatus" ADD VALUE 'inactive';

-- AlterTable
ALTER TABLE "driver_vehicle_documents" ADD COLUMN     "rejection_note" TEXT;

-- AlterTable
ALTER TABLE "user_profiles" ADD COLUMN     "status" "UserProfileStatus" NOT NULL DEFAULT 'pending';
