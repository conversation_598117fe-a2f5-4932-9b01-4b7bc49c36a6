-- CreateTable
CREATE TABLE "driver_city_products" (
    "id" UUID NOT NULL,
    "user_profile_id" UUID NOT NULL,
    "city_product_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_city_products_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "driver_city_products" ADD CONSTRAINT "driver_city_products_user_profile_id_fkey" FOREIGN KEY ("user_profile_id") REFERENCES "user_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_city_products" ADD CONSTRAINT "driver_city_products_city_product_id_fkey" FOREIGN KEY ("city_product_id") REFERENCES "city_products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
