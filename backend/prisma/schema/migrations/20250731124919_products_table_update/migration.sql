-- AlterTable
ALTER TABLE "products" ADD COLUMN     "icon" TEXT,
ADD COLUMN     "identifier" TEXT,
ADD COLUMN     "is_enabled" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "product_service_id" UUID,
ALTER COLUMN "description" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_product_service_id_fkey" FOREIGN KEY ("product_service_id") REFERENCES "product_services"("id") ON DELETE SET NULL ON UPDATE CASCADE;
