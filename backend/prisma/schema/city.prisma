model City {
  id        String     @id @default(uuid()) @map("id") @db.Uuid
  name      String     @map("name")
  icon      String?
  state     String?
  country   String?
  polygon   Json?      @map("polygon")
  h3Indexes String[]   @map("h3_indexes")
  status    CityStatus @default(active) @map("status")
  createdAt DateTime   @default(now()) @map("created_at")
  updatedAt DateTime   @updatedAt @map("updated_at")
  deletedAt DateTime?  @map("deleted_at") @db.Timestamptz

  //relations
  userProfiles UserProfile[]
  cityProducts CityProduct[]

  @@index([name], name: "idx_city_name")
  @@index([h3Indexes], name: "idx_city_h3_indexes")
  @@map("cities")
}

enum CityStatus {
  active
  inactive
}
