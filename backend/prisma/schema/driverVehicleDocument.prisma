model DriverVehicleDocument {
  id                String @id @default(uuid()) @map("id") @db.Uuid
  driverVehicleId   String @map("driver_vehicle_id") @db.Uuid
  vehicleDocumentId String @map("vehicle_document_id") @db.Uuid

  documentUrl    String?               @map("document_url") @db.Text
  documentFields Json?                 @map("document_fields") @db.JsonB
  status         VehicleDocumentStatus @default(PENDING) @map("status")
  rejectionNote  String?   @map("rejection_note") @db.Text
  details        Json                  @map("vehicle_document_fields")
  createdAt      DateTime              @default(now()) @map("created_at")
  updatedAt      DateTime              @updatedAt @map("updated_at")
  deletedAt      DateTime?             @map("deleted_at") @db.Timestamptz

  // Relations
  driverVehicle   DriverVehicle   @relation(fields: [driverVehicleId], references: [id], onDelete: Cascade, name: "DriverVehicleToDriverVehicleDocument")
  vehicleDocument VehicleDocument @relation(fields: [vehicleDocumentId], references: [id])

  @@index([driverVehicleId], name: "idx_driver_vehicle_document_driver_vehicle_id")
  @@map("driver_vehicle_documents")
}

enum VehicleDocumentStatus {
  PENDING
  APPROVED
  REJECTED
}
