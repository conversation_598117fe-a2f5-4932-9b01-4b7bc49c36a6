model VehicleType {
  id          String    @id @default(uuid()) @map("id") @db.Uuid
  name         String    @map("name")
  description  String?
  image        String?   @map("image")
  languageSpec Json?     @map("language_spec") @db.JsonB
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  cityProducts   CityProduct[]
  driverVehicles DriverVehicle[]
  products       Product[]

  @@index([name], name: "idx_vehicle_type_name")
  @@index([languageSpec], name: "idx_vehicle_type_language_spec")
  @@map("vehicle_types")
}
