model DriverCityProduct {
  id             String      @id @default(uuid()) @db.Uuid
  userProfileId  String      @map("user_profile_id") @db.Uuid
  cityProductId  String      @map("city_product_id") @db.Uuid
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")
  deletedAt      DateTime?   @map("deleted_at") @db.Timestamptz

  // Relations
  userProfile    UserProfile @relation(fields: [userProfileId], references: [id])
  cityProduct    CityProduct @relation(fields: [cityProductId], references: [id])

  @@map("driver_city_products")
}