model H3IndexToZone {
  id        String    @id @default(uuid()) @map("id") @db.Uuid
  h3Index   BigInt    @map("h3_index")
  zoneId    String    @map("zone_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  zone Zone @relation(fields: [zoneId], references: [id], onDelete: Cascade)

  @@unique([h3Index, zoneId], name: "unique_h3_index_zone")
  @@index([h3Index], name: "idx_h3_index_to_zone_h3_index")
  @@index([zoneId], name: "idx_h3_index_to_zone_zone_id")
  @@map("h3_index_to_zones")
}
