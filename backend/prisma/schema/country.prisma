model Country {
  id               String            @id @default(uuid()) @map("id") @db.Uuid
  name             String            @map("name")
  iso2             String            @map("iso_2")
  iso3             String?           @map("iso_3")
  phoneCode        String?           @map("phone_code")
  currency         String?           @map("currency")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  deletedAt        DateTime?         @map("deleted_at") @db.Timestamptz
  // City             City[]
  vehicleDocuments VehicleDocument[]
  kycDocuments     KycDocument[]

  @@index([name], name: "idx_country_name")
  @@index([iso2], name: "idx_country_iso2")
  @@map("countries")
}
