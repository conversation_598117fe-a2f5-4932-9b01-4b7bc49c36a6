model ProductService {
    id          String    @id @default(uuid()) @db.Uuid
    name         String    @unique
    description  String?
    icon         String?
    identifier   String?
    languageSpec Json?     @map("language_spec") @db.JsonB
    createdAt    DateTime  @default(now())
    updatedAt    DateTime  @updatedAt
    deletedAt    DateTime?
    
    //relations
    products    Product[]

    @@index([languageSpec], name: "idx_product_service_language_spec")
    @@map("product_services")
}
