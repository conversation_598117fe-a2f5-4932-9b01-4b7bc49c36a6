
We'll use H3 zones of resolution 8 for all zones within our system!

We'll use redis sorted set data structure for storing drivers within these H3 Zones. They key is H3 Zone ID (In this format- drivers:cell:{city}:<h3Index>). So for every H3Cell in our system, there will be a Redis Key, and in that key, we will store the driverIds who're in that cell. The score is the timestamp.

Use ZADD to add a driver to


Drivers Sending Location
----
We'll use /drivers/location endpoint (But websocket using Socket.io and Redis adapter). This endpoint receives the geo location, acknowledges it, and push it to kafka with event DriverLocationReceivedEvent.

Decided to use socket.io because it provides a hybrid approach of websockets with HTTP polling as fallback in case driver goes through area with connection drops.

Design req.

1. High concurrency - must do the jobs as quick as possible to support very high concurrency!
2. Do not do operations that block the main thread for a long time.

Location Ingestor Kafka Consumer 
----

1. Controller - Invokes when DriverLocationReceivedEvent is recieved. Event contains driverId, lat, lon, timestamp, city, status, rideId
2. Convert the lat,lon into H3Index of resolution 8
3. Find the previous h3index of driver by scanning the driver:locations hash with driverId as the key
4. Remove the driver from drivers:cell:<PreviousH3Index> and add him to drivers:cell:<NewH3Index>
5. Update driver meta data hash HSET drivers:<driverId> lat, lon, h3Index, status


Geofence Checker Kafka Consumer
----
1. Controller - Invokes when DriverLocationReceivedEvent is recieved. Event contains driverId, lat, lon, timestamp, city, status, rideId.
2. Convert the lat,lon into H3Index of resolution 8.
3. Fetch the ride information from cache (if rideId is present)
4. Get the pickup lat, lon for the ride, find it's h3Index (res 8), find neighbour with 1 step distance (which means, we're looking at around avg 1km radius).
5. If the driver's h3Index falls in any of the h3Indexes above, call rideService.updateRide(status="arriving"), and also emit DriverReachingPickupPointEvent() which will be caught by notifier and notifies user that driver is nearby!

LocationBroadcaster Kafka Consumer
----
1. Controller - Invokes when DriverLocationReceivedEvent is recieved. Event contains driverId, lat, lon, timestamp, city, status, rideId.
2. Fetch the ride information from cache (if rideId is present)
3. Emit the lat, lon to socket.io channel (rides:rideId) -> So all clients who subscribed to receive updates for this ride will receive the drivers location


Available Rides Search
----
1. Convert pickup lat,lon into h3index of resolution 8
2. Convert destination lat,lon into h3index of resolution 8
3. Find which city/suburb/specific zones does this h3Index belong to (findZonesByH3Index())
4. Find the rearest h3cells (dist 2 or 3) - We'll get probably 1+6+12+18= 37 cells
5. Fetch all drivers by fetching the entire driverIds for each cell (from REDIS SortedSETS) - 37 ZRANGE queries, or a union query to get drivers from all 37 cells at once.. still time complexity o(n) I guess
6. Do route matrix API call with all these drivers (let's assume we get max 100 drivers) to get the ETA.
4. If pickup city and dest city belongs to two different cities, then only intercity and rental products should be shown!
5. If pickup city belongs to a city and dest belongs to no cities, then only intercity and rental should be available!
6. If pickup city and dest city belongs to no cities, then show intercity rides
7. Now we know which all ride types are possible here in this city! Now we'll also confirm if the rides are actually possible based on the driver availability information we have.


When both pickup/location falls outside of a city zone, then intercity is offered, but pickup ETA is not shown. Uber just shows - "Longer wait"

If pickup location is in city zone, then an intermediate location is outside of city zone, but the final destination is again in the same city zone, then CITY rides are shown!

So I think in Uber, if the first and last location falls in the same city, city ride is given!
 
And if the pickup location is very far away from the nearest city zone, probably there aren't any drivers in that region, then it shows no rides available!

Q. How do we decide if we should offer atleast an intercity ride for a given pickup location? FIrst thing is based on whether the pickup/dest falls outside the region.. Another thing is availability of driver.


Availability of driver can be figured out by checking the drivers in nearby cells


Nearby Driver Fetch
----
1. Convert pickup lat,lon into h3index of resolution 8
2.



Adding a new city
---
1. Draw the polygon in front-end, which results in GEOJSON array of lat,lon
2. Feed this array into h3.polygonToCells(res=8) function, we'll get all h3Indexes for this city.
3. Store them in a JSON Array field within cities table
4. Then we have an h3IndexToZones table. For each h3Index, we'll add it to the table as rows with zone_type: city, and zone_id as the cityId
5. h3indexToZones (h3Index: BIGINT, zoneId: int)

zones (id, type: city, suburb, )

Questions:

1. For a city like Bangalore (area: approx 550sq.km ), how many h3Index will be there? 185 indexes!
2. For a zone like airport, how many h3Indexes will be there? 8Indexes!
3. Assuming Tukxi operate in 10000 such cities, with an average 200 zones within city, this means, 2M zones for suburbs with avg 10 h3Index each -> 20M H3Indexes. Combining with 10000 cities having 185hexes -> 1.8M H3Indexes, So, let's assume total of around 25M H3Indexes we need to store!
4. For storing around 30M H3index reverse lookup records, we might need 2.5GB of PGSQL Space -> Which is ok!


Modifying a city
---
1. Redraw the polygon
2. Feed this array into h3.polygonToCells(res=9) function, we'll get all h3Indexes for this city.
3. Replace the JSONArray column with this new cells
4. Find the records for the previous h3Zones, with type=city and id=cityId. Remove these entries.
5. Add the entries for new h3Index for this city

Adding/Editing a zone within a city
---
Same logic as above



Functions
---------

findZonesByH3Index(h3Index: string)
	Ideally, we must scan every zones in our system and see if the given h3Index is present in that h3Index array. But time complexity is O(N), based on number of Zones which will increase linearly with number of Zones. So, NOT RECOMMENDED!

	So we will have a reverse lookup table of all h3INdex(res 8) belonging to every zone in our system...

	Table to query: h3IndexToZones

	So whenever we have an h3index, we do a lookup with O(1) time complexity that results in all zones that area belongs to (such as cities, airports, suburbs etc)



findNearestDrivers(h3Index: string)
	Find all nearby h3Index with distance 5
