# Kafka Consumers

This is an independent microservice that uses Kafka for communication. We will have multiple NestJS modules with controller to receive events, which will be then processed using different service functions.

# Location Ingestor

Responsibility of this module is to act up on `DriverLocationReceivedEvent`, and update the location in Redis stores.

## Data stores

We will have multiple redis stores for managing driver locations, their metadata etc. We must use Repositories to interact with our Redis Store instead of calling the IoRedis functions directly from our services. We must define Models for each data object that we'll be managing.

Controller -> Service -> Repository -> IoRedisClient

### CellDriverEntity
   h3Index: string
   driverIds: string[]

### CellDriverRepository
    findDriverIds: (h3Index: string) => string []
    addDriver: (h3Index: string, driverId: string) => boolean
    removeDriver: (h3Index: string, driverId: string) => boolean

### DriverCell


The other redis store is a HASH that stores key:value pairs of driver:h3Index. This is to know in which cell a driver was last seen.

Another redis store is again a HASH for each driver that stores key:value pairs of driver meta data.

## Functionality

1. Convert the lat,lon into H3Index of resolution 8
2. Find the previous h3index of driver by scanning the driver:locations hash with driverId as the key
3. Remove the driver from the previous