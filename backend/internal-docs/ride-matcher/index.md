# Ride Matcher
Ride matcher service is an asynchronous service that accepts a ride match request, broacast to eligible drivers and finally match one driver.

## Trigger
The trigger for this service is the `RideRequestedEvent` (rideId)

## Inputs

- RideID

## Working

- Fetch the ride from DB based on given Ride ID
- Check if the pickup location belongs to a special zone that has a specific ride matching algorithm.
- Select the ride matching algorithm
- Run the ride matching algorithm to get the list of drivers by groups
- For each driver in group send the ride request and emit - `RideOfferedEvent` (rideId, driverId)
- Save the driver id to the input ride
- Emit event - `RideMatchSuccessfulEvent` (rideId, driverId)