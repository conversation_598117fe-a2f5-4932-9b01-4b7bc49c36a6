# Ride Lifecycle
The lifecycle of a ride is as follows

Requested: A ride is requested by the rider. This is the default status.
Processing: The RideMatcher engine has picked up this job and is now matching to the most efficient available driver.
Accepted: A driver is assigned and is on the way to the pickup location.
Arriving: The driver has arrived or is within 0.2 miles of the pickup point.
In Progress: The trip is underway, from pickup to dropoff.
Completed: The trip has been completed by the driver.
Cancelled: The ride has been cancelled by the driver or rider.