# Ride Matching Algorithm
The system allows specific pickup zones to have specific ride matching algorithms. For example, airport pickup zones need a different ride matching algorithm based on FIFO since drivers might be queued up for rides.

# Responsibility
The responsibility of this service is to find the drivers, score them, rank them, split it into priority groups and return the groups.

# Working
The algorithm is nothing but a service class, with an `execute()` function