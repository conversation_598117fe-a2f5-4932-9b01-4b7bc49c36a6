'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Calendar } from '@/components/ui/calendar';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useFileUpload, formatBytes } from '@/hooks/use-file-upload';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { AlertCircle, FileText, Upload, X, Loader2, CalendarIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { z } from 'zod';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { useAddKycDocument, useEditKycDocument } from '../api/mutations';
import { KycDocument } from '../types/driver';
import { validateAadhaarNumber, validateDrivingLicenseNumber, formatAadhaarNumber, formatDrivingLicenseNumber } from '../utils/document-validation';

// Form validation schemas for each document type
const aadhaarSchema = z.object({
   documentNumber: z
      .string()
      .min(1, 'Aadhaar number is required')
      .refine(
         (value) => validateAadhaarNumber(value),
         {
            message: 'Please enter a valid 12-digit Aadhaar number',
         }
      ),
});

const drivingLicenseSchema = z.object({
   documentNumber: z
      .string()
      .min(1, 'License number is required')
      .refine(
         (value) => validateDrivingLicenseNumber(value),
         {
            message: 'Please enter a valid driving license number (e.g., *************** or ***************)',
         }
      ),
   expiryDate: z
      .date({
         message: 'Expiry date is required',
      })
      .refine(
         date => {
            const today = new Date();
            return date > today;
         },
         {
            message: 'Expiry date must be in the future',
         }
      ),
});

const bankDetailsSchema = z.object({
   account_number: z
      .string()
      .min(1, 'Account number is required')
      .regex(/^\d+$/, 'Account number should contain digits only.'),
   ifsc_code: z
      .string()
      .min(1, 'IFSC code is required')
      .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Please enter a valid IFSC code in the format: XXXX0YYYYYY (e.g., SBIN0001234).'),
   account_holder_name: z
      .string()
      .min(1, 'Account holder name is required')
      .regex(/^[A-Za-z\s]+$/, 'Account holder name should contain only letters and spaces.'),
   bank_name: z
      .string()
      .min(1, 'Bank name is required')
      .regex(/^[A-Za-z\s]+$/, 'Bank name should contain only letters and spaces.'),
});

const policeClearanceSchema = z.object({
   certificate_number: z.string().min(1, 'Certificate number is required'),
   issue_date: z.date({
      message: 'Issue date is required',
   }),
   expiry_date: z
      .date({
         message: 'Expiry date is required',
      })
      .refine(
         date => {
            const today = new Date();
            return date > today;
         },
         {
            message: 'Expiry date must be in the future',
         }
      ),
   issuing_authority: z.string().min(1, 'Issuing authority is required'),
});

type FormData = {
   documentNumber?: string;
   expiryDate?: Date;
   account_number?: string;
   ifsc_code?: string;
   account_holder_name?: string;
   bank_name?: string;
   certificate_number?: string;
   issue_date?: Date;
   expiry_date?: Date;
   issuing_authority?: string;
};

interface KycDocumentUploadProps {
   document: KycDocument;
   driverId: string;
   open: boolean;
   onClose: () => void;
   onSuccess: () => void;
   editMode?: boolean; // Whether we're editing an existing document (re-upload)
}

export function KycDocumentUploadModal({
   document,
   driverId,
   open,
   onClose,
   onSuccess,
   editMode = false,
}: KycDocumentUploadProps) {
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [fileError, setFileError] = useState<string>('');
   const [expiryDateOpen, setExpiryDateOpen] = useState(false);
   const [issueDateOpen, setIssueDateOpen] = useState(false);
   const [pccExpiryDateOpen, setPccExpiryDateOpen] = useState(false);

   // Get the appropriate schema based on document identifier
   const getSchema = () => {
      switch (document.identifier) {
         case 'aadhaar_card':
            return aadhaarSchema;
         case 'driving_licence':
            return drivingLicenseSchema;
         case 'bank_details':
            return bankDetailsSchema;
         case 'police_clearance_certificate':
            return policeClearanceSchema;
         default:
            return z.object({});
      }
   };

   const form = useForm<FormData>({
      resolver: zodResolver(getSchema()) as any,
      mode: 'onChange', // This will validate on change and clear errors as user types
      defaultValues: {},
   });

   const fileUploadMutation = useFileUploadMutation();
   const addKycDocumentMutation = useAddKycDocument();
   const editKycDocumentMutation = useEditKycDocument();

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.pdf,.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
      }
   }, [file]);

   // Check if document supports Digilocker (only Aadhaar card and driving license)
   const supportsDigilocker =
      document.identifier === 'aadhaar_card' || document.identifier === 'driving_licence';

   // Clear files and errors when modal opens, populate form in edit mode
   useEffect(() => {
      if (open) {
         clearFiles();
         clearErrors();
         setFileError('');

         // In edit mode, populate form with existing data
         if (editMode && document.driverKyc) {
            const existingData: FormData = {};

            // Direct fields
            if (document.driverKyc.documentNumber) {
               existingData.documentNumber = document.driverKyc.documentNumber;
            }
            if (document.driverKyc.expiryDate) {
               existingData.expiryDate = new Date(document.driverKyc.expiryDate); // Convert string to Date
            }

            // Document fields
            if (document.driverKyc.documentFields) {
               const fields = document.driverKyc.documentFields;
               if (fields.account_number) existingData.account_number = fields.account_number;
               if (fields.ifsc_code) existingData.ifsc_code = fields.ifsc_code;
               if (fields.account_holder_name)
                  existingData.account_holder_name = fields.account_holder_name;
               if (fields.bank_name) existingData.bank_name = fields.bank_name;
               if (fields.certificate_number)
                  existingData.certificate_number = fields.certificate_number;
               if (fields.issue_date) existingData.issue_date = new Date(fields.issue_date); // Convert string to Date
               if (fields.expiry_date) existingData.expiry_date = new Date(fields.expiry_date); // Convert string to Date
               if (fields.issuing_authority)
                  existingData.issuing_authority = fields.issuing_authority;
            }

            form.reset(existingData);
         } else {
            // Explicitly clear all possible form fields to ensure clean state
            const clearData: FormData = {
               documentNumber: '',
               expiryDate: undefined,
               account_number: '',
               ifsc_code: '',
               account_holder_name: '',
               bank_name: '',
               certificate_number: '',
               issue_date: undefined,
               expiry_date: undefined,
               issuing_authority: '',
            };
            form.reset(clearData);
         }
      }
   }, [open, clearFiles, clearErrors, form, editMode, document.driverKyc]);

   // Clear files and errors when modal is closed
   const handleClose = () => {
      clearFiles();
      clearErrors();
      setFileError('');
      form.reset();
      onClose();
   };

   const handleSubmit = async () => {
      // Clear previous file error
      setFileError('');

      // Validate both file and form data simultaneously
      let hasErrors = false;

      // Check for file - only required if not in edit mode
      if (!file && !editMode) {
         setFileError('Please select a file to upload');
         hasErrors = true;
      }

      // Validate form data
      const formData = form.getValues();
      const validationResult = getSchema().safeParse(formData);

      if (!validationResult.success) {
         // Trigger form validation to show errors
         form.trigger();
         hasErrors = true;
      }

      // If either file or form validation failed, don't proceed
      if (hasErrors) {
         return;
      }

      setIsSubmitting(true);

      try {
         // Step 1: Upload file to S3 (only if a new file is selected)
         let documentUrl = document.driverKyc?.documentUrl; // Keep existing URL by default

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            documentUrl = uploadResponse.data.key;
         }

         // Prepare request data based on document type
         const requestData: any = {
            userProfileId: driverId,
            kycDocumentId: document.id,
            documentUrl: documentUrl,
            fromDigilocker: false,
         };

         // Add fields based on document type
         if (document.identifier === 'aadhaar_card' || document.identifier === 'driving_licence') {
            if (formData.documentNumber) {
               requestData.documentNumber = formData.documentNumber;
            }
            if (formData.expiryDate && document.identifier === 'driving_licence') {
               requestData.expiryDate = formData.expiryDate.toISOString().split('T')[0]; // Convert Date to YYYY-MM-DD string
            }
         } else if (
            document.identifier === 'bank_details' ||
            document.identifier === 'police_clearance_certificate'
         ) {
            // For bank details and police clearance, use documentFields
            const documentFields: Record<string, any> = {};

            if (document.identifier === 'bank_details') {
               if (formData.account_number) documentFields.account_number = formData.account_number;
               if (formData.ifsc_code) documentFields.ifsc_code = formData.ifsc_code;
               if (formData.account_holder_name)
                  documentFields.account_holder_name = formData.account_holder_name;
               if (formData.bank_name) documentFields.bank_name = formData.bank_name;
            } else if (document.identifier === 'police_clearance_certificate') {
               if (formData.certificate_number)
                  documentFields.certificate_number = formData.certificate_number;
               if (formData.issue_date)
                  documentFields.issue_date = formData.issue_date.toISOString().split('T')[0]; // Convert Date to YYYY-MM-DD string
               if (formData.expiry_date)
                  documentFields.expiry_date = formData.expiry_date.toISOString().split('T')[0]; // Convert Date to YYYY-MM-DD string
               if (formData.issuing_authority)
                  documentFields.issuing_authority = formData.issuing_authority;
            }

            if (Object.keys(documentFields).length > 0) {
               requestData.documentFields = documentFields;
            }
         }

         if ((editMode && document.driverKyc) || document.driverKyc?.status === 'REJECTED') {
            // Step 2a: Edit existing KYC document (re-upload scenario or rejected document)
            const editRequestData: any = {
               id: document.driverKyc.id,
               documentUrl: documentUrl, // Use the documentUrl we prepared above
            };

            // Add fields based on document type
            if (
               document.identifier === 'aadhaar_card' ||
               document.identifier === 'driving_licence'
            ) {
               if (formData.documentNumber) {
                  editRequestData.documentNumber = formData.documentNumber;
               }
               if (formData.expiryDate && document.identifier === 'driving_licence') {
                  editRequestData.expiryDate = formData.expiryDate.toISOString().split('T')[0]; // Convert Date to YYYY-MM-DD string
               }
            } else if (
               document.identifier === 'bank_details' ||
               document.identifier === 'police_clearance_certificate'
            ) {
               // For bank details and police clearance, use documentFields
               const documentFields: Record<string, any> = {};

               if (document.identifier === 'bank_details') {
                  if (formData.account_number)
                     documentFields.account_number = formData.account_number;
                  if (formData.ifsc_code) documentFields.ifsc_code = formData.ifsc_code;
                  if (formData.account_holder_name)
                     documentFields.account_holder_name = formData.account_holder_name;
                  if (formData.bank_name) documentFields.bank_name = formData.bank_name;
               } else if (document.identifier === 'police_clearance_certificate') {
                  if (formData.certificate_number)
                     documentFields.certificate_number = formData.certificate_number;
                  if (formData.issue_date)
                     documentFields.issue_date = formData.issue_date.toISOString().split('T')[0]; // Convert Date to YYYY-MM-DD string
                  if (formData.expiry_date)
                     documentFields.expiry_date = formData.expiry_date.toISOString().split('T')[0]; // Convert Date to YYYY-MM-DD string
                  if (formData.issuing_authority)
                     documentFields.issuing_authority = formData.issuing_authority;
               }

               if (Object.keys(documentFields).length > 0) {
                  editRequestData.documentFields = documentFields;
               }
            }

            await editKycDocumentMutation.mutateAsync(editRequestData);
            toast.success(
               document.driverKyc?.status === 'REJECTED'
                  ? 'Document uploaded successfully'
                  : 'Document re-uploaded successfully'
            );
         } else {
            // Step 2b: Add new KYC document
            await addKycDocumentMutation.mutateAsync(requestData);
            toast.success('Document uploaded successfully');
         }

         onSuccess();
      } catch (error) {
         console.error('Upload error:', error);
         toast.error('Failed to upload document. Please try again.');
      } finally {
         setIsSubmitting(false);
      }
   };

   return (
      <Dialog open={open} onOpenChange={handleClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-2xl max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>Upload {document.name}</DialogTitle>
               <DialogDescription>
                  Upload your {document.name.toLowerCase()} document for verification
               </DialogDescription>
            </DialogHeader>

            <div className='space-y-6'>
               {/* Digilocker Option - Only for Aadhaar card and driving license */}
               {supportsDigilocker && (
                  <>
                     <Card className='p-4 bg-blue-50 border-blue-200'>
                        <div className='flex items-center justify-between'>
                           <div>
                              <h4 className='font-medium text-blue-900'>Fetch from Digilocker</h4>
                              <p className='text-sm text-blue-700 mt-1'>
                                 Get your document directly from Digilocker
                              </p>
                           </div>
                           <Button variant='outline' disabled className='bg-white'>
                              Fetch from Digilocker
                           </Button>
                        </div>
                     </Card>

                     <div className='text-center text-sm text-gray-500'>OR UPLOAD MANUALLY</div>
                  </>
               )}

               {/* File Upload Area */}
               <div className='space-y-4'>
                  <div
                     role='button'
                     onClick={openFileDialog}
                     onDragEnter={handleDragEnter}
                     onDragLeave={handleDragLeave}
                     onDragOver={handleDragOver}
                     onDrop={handleDrop}
                     data-dragging={isDragging || undefined}
                     className='border-input cursor-pointer hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]'
                  >
                     <input
                        {...getInputProps()}
                        className='sr-only'
                        aria-label='Upload file'
                        disabled={Boolean(file)}
                     />

                     <div className='flex flex-col items-center justify-center text-center'>
                        <div
                           className='bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border'
                           aria-hidden='true'
                        >
                           <Upload className='size-4 opacity-60' />
                        </div>
                        <p className='mb-1.5 text-sm font-medium'>
                           {document.identifier === 'bank_details'
                              ? 'Upload Supporting Document'
                              : 'Upload Document'}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                           {document.identifier === 'bank_details'
                              ? 'Upload a supporting document like a cancelled cheque or passbook'
                              : 'Drag & drop or click to browse'}{' '}
                           (max. {formatBytes(maxSize)})
                        </p>
                        <p className='text-muted-foreground text-xs mt-1'>
                           Supported: JPEG, PNG, PDF
                        </p>
                     </div>
                  </div>
                  {errors.length > 0 && (
                     <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                        <AlertCircle className='size-3 shrink-0' />
                        <span>{errors[0]}</span>
                     </div>
                  )}
                  {fileError && (
                     <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                        <AlertCircle className='size-3 shrink-0' />
                        <span>{fileError}</span>
                     </div>
                  )}
                  {/* File Preview - Show either newly selected file or existing document */}
                  {(file || (editMode && document.driverKyc?.documentUrl)) && (
                     <Card className='p-4'>
                        <div className='flex items-center justify-between'>
                           <div className='flex items-center gap-3'>
                              <div className='flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg'>
                                 <FileText className='w-5 h-5 text-blue-600' />
                              </div>
                              <div>
                                 {file ? (
                                    <>
                                       <p className='text-sm font-medium text-gray-900'>
                                          {file.file.name}
                                       </p>
                                       <p className='text-xs text-gray-500'>
                                          {formatBytes(file.file.size)}
                                       </p>
                                    </>
                                 ) : (
                                    <>
                                       <p className='text-sm font-medium text-gray-900'>
                                          Current Document
                                       </p>
                                       <p className='text-xs text-gray-500'>Uploaded document</p>
                                    </>
                                 )}
                              </div>
                           </div>
                           <div className='flex items-center gap-2'>
                              {!file && editMode && document.driverKyc?.documentUrl && (
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                       window.open(document.driverKyc!.documentUrl, '_blank')
                                    }
                                    className='text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300'
                                 >
                                    View
                                 </Button>
                              )}
                              {file && (
                                 <Button
                                    variant='ghost'
                                    size='sm'
                                    onClick={() => removeFile(file.id)}
                                    className='text-red-600 hover:text-red-700'
                                 >
                                    <X className='w-4 h-4' />
                                 </Button>
                              )}
                           </div>
                        </div>
                     </Card>
                  )}

                  {/* Form Fields - Compact design below file upload */}
                  {(document.identifier === 'aadhaar_card' ||
                     document.identifier === 'driving_licence' ||
                     document.identifier === 'bank_details' ||
                     document.identifier === 'police_clearance_certificate') && (
                     <div className='space-y-3'>
                        {/* Aadhaar Card Fields */}
                        {document.identifier === 'aadhaar_card' && (
                           <div>
                              <Label htmlFor='documentNumber' className='text-sm font-medium'>
                                 Aadhaar Number *
                              </Label>
                              <Input
                                 id='documentNumber'
                                 {...form.register('documentNumber', {
                                    onChange: e => {
                                       const formatted = formatAadhaarNumber(e.target.value);
                                       e.target.value = formatted;
                                    },
                                 })}
                                 placeholder='Enter Aadhaar number (e.g., 1234 5678 9012)'
                                 maxLength={14}
                                 className='mt-1'
                              />
                              {form.formState.errors.documentNumber && (
                                 <p className='text-red-500 text-xs mt-1'>
                                    {form.formState.errors.documentNumber.message}
                                 </p>
                              )}
                           </div>
                        )}

                        {/* Driving License Fields */}
                        {document.identifier === 'driving_licence' && (
                           <div className='space-y-3'>
                              <div>
                                 <Label htmlFor='documentNumber' className='text-sm font-medium'>
                                    License Number *
                                 </Label>
                                 <Input
                                    id='documentNumber'
                                    {...form.register('documentNumber', {
                                       onChange: e => {
                                          const formatted = formatDrivingLicenseNumber(
                                             e.target.value
                                          );
                                          e.target.value = formatted;
                                       },
                                    })}
                                    placeholder='Enter license number (e.g., ***************)'
                                    maxLength={16}
                                    className='mt-1'
                                 />
                                 {form.formState.errors.documentNumber && (
                                    <p className='text-red-500 text-xs mt-1'>
                                       {form.formState.errors.documentNumber.message}
                                    </p>
                                 )}
                              </div>
                              <div>
                                 <Label htmlFor='expiryDate' className='text-sm font-medium'>
                                    Expiry Date *
                                 </Label>
                                 <Controller
                                    name='expiryDate'
                                    control={form.control}
                                    render={({ field }) => (
                                       <Popover
                                          open={expiryDateOpen}
                                          onOpenChange={setExpiryDateOpen}
                                       >
                                          <PopoverTrigger asChild>
                                             <Button
                                                variant='outline'
                                                className={cn(
                                                   'w-full justify-start text-left font-normal mt-1',
                                                   !field.value && 'text-muted-foreground'
                                                )}
                                             >
                                                {field.value ? (
                                                   format(field.value, 'PPP')
                                                ) : (
                                                   <span>Pick expiry date</span>
                                                )}
                                                <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                                             </Button>
                                          </PopoverTrigger>
                                          <PopoverContent
                                             className='w-auto p-0 z-[9999]'
                                             align='start'
                                          >
                                             <Calendar
                                                mode='single'
                                                selected={field.value}
                                                onSelect={date => {
                                                   field.onChange(date);
                                                   setExpiryDateOpen(false);
                                                }}
                                                captionLayout='dropdown'
                                                defaultMonth={field.value || new Date()}
                                                disabled={date => {
                                                   const today = new Date();
                                                   today.setHours(0, 0, 0, 0);
                                                   return date <= today; // Disable past dates and today
                                                }}
                                                fromYear={new Date().getFullYear()}
                                                toYear={new Date().getFullYear() + 20}
                                                initialFocus
                                             />
                                          </PopoverContent>
                                       </Popover>
                                    )}
                                 />
                                 {form.formState.errors.expiryDate && (
                                    <p className='text-red-500 text-xs mt-1'>
                                       {form.formState.errors.expiryDate.message}
                                    </p>
                                 )}
                              </div>
                           </div>
                        )}

                        {/* Bank Details Fields */}
                        {document.identifier === 'bank_details' && (
                           <div className='space-y-3'>
                              <div className='grid grid-cols-2 gap-3'>
                                 <div>
                                    <Label htmlFor='account_number' className='text-sm font-medium'>
                                       Account Number *
                                    </Label>
                                    <Input
                                       id='account_number'
                                       {...form.register('account_number')}
                                       placeholder='Account number'
                                       className='mt-1'
                                    />
                                    {form.formState.errors.account_number && (
                                       <p className='text-red-500 text-xs mt-1'>
                                          {form.formState.errors.account_number.message}
                                       </p>
                                    )}
                                 </div>
                                 <div>
                                    <Label htmlFor='ifsc_code' className='text-sm font-medium'>
                                       IFSC Code *
                                    </Label>
                                    <Input
                                       id='ifsc_code'
                                       {...form.register('ifsc_code')}
                                       placeholder='IFSC code'
                                       className='mt-1'
                                    />
                                    {form.formState.errors.ifsc_code && (
                                       <p className='text-red-500 text-xs mt-1'>
                                          {form.formState.errors.ifsc_code.message}
                                       </p>
                                    )}
                                 </div>
                              </div>
                              <div className='grid grid-cols-2 gap-3'>
                                 <div>
                                    <Label
                                       htmlFor='account_holder_name'
                                       className='text-sm font-medium'
                                    >
                                       Account Holder Name *
                                    </Label>
                                    <Input
                                       id='account_holder_name'
                                       {...form.register('account_holder_name')}
                                       placeholder='Holder name'
                                       className='mt-1'
                                    />
                                    {form.formState.errors.account_holder_name && (
                                       <p className='text-red-500 text-xs mt-1'>
                                          {form.formState.errors.account_holder_name.message}
                                       </p>
                                    )}
                                 </div>
                                 <div>
                                    <Label htmlFor='bank_name' className='text-sm font-medium'>
                                       Bank Name *
                                    </Label>
                                    <Input
                                       id='bank_name'
                                       {...form.register('bank_name')}
                                       placeholder='Bank name'
                                       className='mt-1'
                                    />
                                    {form.formState.errors.bank_name && (
                                       <p className='text-red-500 text-xs mt-1'>
                                          {form.formState.errors.bank_name.message}
                                       </p>
                                    )}
                                 </div>
                              </div>
                           </div>
                        )}

                        {/* Police Clearance Certificate Fields */}
                        {document.identifier === 'police_clearance_certificate' && (
                           <div className='space-y-3'>
                              <div>
                                 <Label
                                    htmlFor='certificate_number'
                                    className='text-sm font-medium'
                                 >
                                    Certificate Number *
                                 </Label>
                                 <Input
                                    id='certificate_number'
                                    {...form.register('certificate_number')}
                                    placeholder='Certificate number'
                                    className='mt-1'
                                 />
                                 {form.formState.errors.certificate_number && (
                                    <p className='text-red-500 text-xs mt-1'>
                                       {form.formState.errors.certificate_number.message}
                                    </p>
                                 )}
                              </div>
                              <div className='grid grid-cols-2 gap-3'>
                                 <div>
                                    <Label htmlFor='issue_date' className='text-sm font-medium'>
                                       Issue Date *
                                    </Label>
                                    <Controller
                                       name='issue_date'
                                       control={form.control}
                                       render={({ field }) => (
                                          <Popover
                                             open={issueDateOpen}
                                             onOpenChange={setIssueDateOpen}
                                          >
                                             <PopoverTrigger asChild>
                                                <Button
                                                   variant='outline'
                                                   className={cn(
                                                      'w-full justify-start text-left font-normal mt-1',
                                                      !field.value && 'text-muted-foreground'
                                                   )}
                                                >
                                                   {field.value ? (
                                                      format(field.value, 'PPP')
                                                   ) : (
                                                      <span>Pick issue date</span>
                                                   )}
                                                   <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                                                </Button>
                                             </PopoverTrigger>
                                             <PopoverContent
                                                className='w-auto p-0 z-[9999]'
                                                align='start'
                                             >
                                                <Calendar
                                                   mode='single'
                                                   selected={field.value}
                                                   onSelect={date => {
                                                      field.onChange(date);
                                                      setIssueDateOpen(false);
                                                   }}
                                                   captionLayout='dropdown'
                                                   defaultMonth={field.value || new Date()}
                                                   disabled={date => {
                                                      const today = new Date();
                                                      return date > today; // Disable future dates for issue date
                                                   }}
                                                   fromYear={1990}
                                                   toYear={new Date().getFullYear()}
                                                   initialFocus
                                                />
                                             </PopoverContent>
                                          </Popover>
                                       )}
                                    />
                                    {form.formState.errors.issue_date && (
                                       <p className='text-red-500 text-xs mt-1'>
                                          {form.formState.errors.issue_date.message}
                                       </p>
                                    )}
                                 </div>
                                 <div>
                                    <Label htmlFor='expiry_date' className='text-sm font-medium'>
                                       Expiry Date *
                                    </Label>
                                    <Controller
                                       name='expiry_date'
                                       control={form.control}
                                       render={({ field }) => (
                                          <Popover
                                             open={pccExpiryDateOpen}
                                             onOpenChange={setPccExpiryDateOpen}
                                          >
                                             <PopoverTrigger asChild>
                                                <Button
                                                   variant='outline'
                                                   className={cn(
                                                      'w-full justify-start text-left font-normal mt-1',
                                                      !field.value && 'text-muted-foreground'
                                                   )}
                                                >
                                                   {field.value ? (
                                                      format(field.value, 'PPP')
                                                   ) : (
                                                      <span>Pick expiry date</span>
                                                   )}
                                                   <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                                                </Button>
                                             </PopoverTrigger>
                                             <PopoverContent
                                                className='w-auto p-0 z-[9999]'
                                                align='start'
                                             >
                                                <Calendar
                                                   mode='single'
                                                   selected={field.value}
                                                   onSelect={date => {
                                                      field.onChange(date);
                                                      setPccExpiryDateOpen(false);
                                                   }}
                                                   captionLayout='dropdown'
                                                   defaultMonth={field.value || new Date()}
                                                   disabled={date => {
                                                      const today = new Date();
                                                      today.setHours(0, 0, 0, 0);
                                                      return date <= today; // Disable past dates and today
                                                   }}
                                                   fromYear={new Date().getFullYear()}
                                                   toYear={new Date().getFullYear() + 10}
                                                   initialFocus
                                                />
                                             </PopoverContent>
                                          </Popover>
                                       )}
                                    />
                                    {form.formState.errors.expiry_date && (
                                       <p className='text-red-500 text-xs mt-1'>
                                          {form.formState.errors.expiry_date.message}
                                       </p>
                                    )}
                                 </div>
                              </div>
                              <div>
                                 <Label htmlFor='issuing_authority' className='text-sm font-medium'>
                                    Issuing Authority *
                                 </Label>
                                 <Input
                                    id='issuing_authority'
                                    {...form.register('issuing_authority')}
                                    placeholder='Issuing authority'
                                    className='mt-1'
                                 />
                                 {form.formState.errors.issuing_authority && (
                                    <p className='text-red-500 text-xs mt-1'>
                                       {form.formState.errors.issuing_authority.message}
                                    </p>
                                 )}
                              </div>
                           </div>
                        )}
                     </div>
                  )}

                  {/* Action Buttons */}
                  <div className='flex gap-3 pt-4'>
                     <Button variant='outline' onClick={handleClose} className='flex-1'>
                        Cancel
                     </Button>
                     <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className='flex-1 gap-2'
                     >
                        {isSubmitting ? (
                           <>
                              <Loader2 className='w-4 h-4 animate-spin' />
                              Uploading...
                           </>
                        ) : (
                           <>
                              <Upload className='w-4 h-4' />
                              Submit
                           </>
                        )}
                     </Button>
                  </div>
               </div>
            </div>
         </DialogContent>
      </Dialog>
   );
}
