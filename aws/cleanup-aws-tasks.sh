#!/bin/bash

# Script to clean up old/unwanted AWS ECS task definition revisions
# This script lists all task definitions for the tukxi project

set -e

REGION="ap-south-1"
CLUSTER="tukxi-staging-cluster"

echo "🔍 Listing all task definitions in region: $REGION"
echo "======================================================"

# List all task definition families
FAMILIES=$(aws ecs list-task-definition-families --region $REGION --status ACTIVE --output text --query 'families[]')

for FAMILY in $FAMILIES; do
    if [[ $FAMILY == *"api"* ]] || [[ $FAMILY == *"kafka"* ]] || [[ $FAMILY == *"rabbit"* ]] || [[ $FAMILY == *"migration"* ]]; then
        echo ""
        echo "📋 Task Definition Family: $FAMILY"
        echo "----------------------------------------"
        
        # List all revisions for this family
        aws ecs list-task-definitions --region $REGION --family-prefix $FAMILY --output table --query 'taskDefinitionArns[]'
        
        # Show currently running tasks using this family
        TASKS=$(aws ecs list-tasks --cluster $CLUSTER --family $FAMILY --query 'taskArns[]' --output text 2>/dev/null || echo "")
        if [ -n "$TASKS" ]; then
            echo "🏃 Currently running tasks:"
            echo "$TASKS"
        else
            echo "💤 No currently running tasks"
        fi
    fi
done

echo ""
echo "⚠️  CLEANUP COMMANDS (run manually if needed):"
echo "=============================================="
echo "# To deregister a specific task definition revision:"
echo "# aws ecs deregister-task-definition --task-definition FAMILY_NAME:REVISION_NUMBER"
echo ""
echo "# Example:"
echo "# aws ecs deregister-task-definition --task-definition api:1"
echo ""
echo "# Note: Only deregister revisions that are not currently in use by services!"
