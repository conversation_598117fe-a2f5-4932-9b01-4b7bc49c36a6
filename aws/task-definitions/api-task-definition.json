{"taskDefinitionArn": "arn:aws:ecs:ap-south-1:111311033809:task-definition/api:1", "containerDefinitions": [{"name": "api-container", "image": "111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:latest", "cpu": 0, "portMappings": [{"name": "tuxi-node-api", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::tuxi-staging-bucket/server-variables/.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/api", "awslogs-create-group": "true", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "api", "executionRoleArn": "arn:aws:iam::111311033809:role/ecsTaskExecutionRole", "networkMode": "bridge", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.env-files.s3"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2"], "requiresCompatibilities": ["EC2"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2025-08-11T12:39:09.866Z", "registeredBy": "arn:aws:iam::111311033809:root", "enableFaultInjection": false, "tags": []}