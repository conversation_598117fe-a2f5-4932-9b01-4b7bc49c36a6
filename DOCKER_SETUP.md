# Tukxi Microservices Docker Setup

This document provides a complete overview of the Docker setup for the Tukxi microservices architecture.

## 🚀 Quick Start

### Development Environment
```bash
# Start all services
./scripts/docker-dev.sh start

# View logs
./scripts/docker-dev.sh logs

# Stop services
./scripts/docker-dev.sh stop
```

### Production Environment
```bash
# Start production services
./scripts/docker-prod.sh start

# Scale services
./scripts/docker-prod.sh scale api 3

# Update services
./scripts/docker-prod.sh update
```

## 📁 File Structure

```
├── docker/
│   ├── README.md                     # Detailed Docker documentation
│   ├── api.dev.Dockerfile           # Development API container
│   ├── api.prod.Dockerfile          # Production API container
│   ├── kafka-consumers.dev.Dockerfile
│   ├── kafka-consumers.prod.Dockerfile
│   ├── notifier.dev.Dockerfile
│   ├── notifier.prod.Dockerfile
│   ├── ride-matcher.dev.Dockerfile
│   └── ride-matcher.prod.Dockerfile
├── scripts/
│   ├── docker-dev.sh                # Development management script
│   └── docker-prod.sh               # Production management script
├── backend/
│   ├── .env                         # Local development environment
│   └── .env.docker                  # Docker-specific environment
├── docker-compose.yml               # Development compose file
└── docker-compose.prod.yml          # Production compose file
```

## 🏗️ Architecture

### Services Overview

| Service | Port | Purpose | Dependencies |
|---------|------|---------|--------------|
| **API** | 3000 | Main REST API | PostgreSQL, Redis, Kafka, RabbitMQ |
| **Kafka Consumers** | 3001 | Message processing | PostgreSQL, Redis, Kafka |
| **Notifier** | 3002 | Notifications | PostgreSQL, Redis |
| **Ride Matcher** | 3003 | Ride matching logic | PostgreSQL, Redis, RabbitMQ |

### Infrastructure Services

| Service | Port | Purpose | Management UI |
|---------|------|---------|---------------|
| **PostgreSQL** | 5432 | Primary database | - |
| **Redis** | 6379 | Cache & sessions | - |
| **Kafka** | 9092 | Event streaming (KRaft mode) | - |
| **RabbitMQ** | 5672 | Message broker | http://localhost:15672 |

## 🔧 Configuration

### Environment Files

- **`.env`**: Local development (localhost connections)
- **`.env.docker`**: Docker environment (service hostnames)

### Key Environment Variables

```bash
# Database
DATABASE_HOST=pgsql
DATABASE_URL="**********************************************/tukxi"

# Cache
REDIS_HOST=redis

# Message Brokers
KAFKA_BROKERS=kafka:9092
RABBITMQ_URL=amqp://tukxi:password@rabbitmq:5672

# Service Ports
KAFKA_CONSUMER_PORT=3001
NOTIFIER_PORT=3002
RIDE_MATCHER_PORT=3003
```

## 🛠️ Management Scripts

### Development Script (`./scripts/docker-dev.sh`)

```bash
start           # Start all services
start-infra     # Start only infrastructure
start-micro     # Start only microservices
stop            # Stop all services
restart         # Restart all services
logs [service]  # View logs
status          # Show service status
rebuild [service] # Rebuild services
cleanup         # Remove all containers/volumes
```

### Production Script (`./scripts/docker-prod.sh`)

```bash
start           # Start all services
stop            # Stop all services
scale <service> <replicas> # Scale service
update          # Zero-downtime update
backup          # Backup databases
logs [service]  # View logs
status          # Show service status
```

## 🔍 Monitoring & Health Checks

### Health Endpoints
- API: `http://localhost:3000/health`
- Kafka Consumers: `http://localhost:3001/health`
- Notifier: `http://localhost:3002/health`
- Ride Matcher: `http://localhost:3003/health`

### Management UIs
- RabbitMQ: `http://localhost:15672` (user: tukxi, pass: password)

### Useful Commands

```bash
# Check all service status
docker-compose ps

# View specific service logs
docker-compose logs -f kafka-consumers

# Execute command in container
docker-compose exec api bash

# Check resource usage
docker stats

# View networks
docker network ls
```

## 🚨 Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check what's using the port
   lsof -i :3000
   
   # Kill process using port
   kill -9 $(lsof -t -i:3000)
   ```

2. **Memory Issues**
   ```bash
   # Increase Docker memory in Docker Desktop
   # Or reduce service memory limits in docker-compose.prod.yml
   ```

3. **Network Issues**
   ```bash
   # Recreate network
   docker network rm tukxi-network
   docker-compose up -d
   ```

4. **Database Connection Issues**
   ```bash
   # Check if PostgreSQL is ready
   docker-compose exec pgsql pg_isready -U tukxi_db_user
   
   # View database logs
   docker-compose logs pgsql
   ```

5. **Kafka Connection Issues**
   ```bash
   # Check Kafka broker
   docker-compose exec kafka kafka-broker-api-versions --bootstrap-server localhost:9092
   
   # List topics
   docker-compose exec kafka kafka-topics --list --bootstrap-server localhost:9092
   ```

### Reset Everything

```bash
# Complete cleanup and restart
./scripts/docker-dev.sh cleanup
./scripts/docker-dev.sh start
```

## 🔒 Security Notes

### Development
- Default passwords for convenience
- All ports exposed for debugging
- No TLS encryption

### Production
- Change default passwords
- Use Docker secrets for sensitive data
- Implement proper firewall rules
- Enable TLS for external communications
- Use environment-specific configurations

## 📊 Performance Tuning

### Resource Allocation
- Adjust memory limits in `docker-compose.prod.yml`
- Scale services based on load: `./scripts/docker-prod.sh scale api 3`
- Monitor with `docker stats`

### Database Optimization
- Tune PostgreSQL settings for your workload
- Use connection pooling
- Monitor query performance

### Cache Optimization
- Configure Redis memory policies
- Monitor cache hit rates
- Implement proper cache invalidation

## 🔄 CI/CD Integration

The Docker setup is designed to work with CI/CD pipelines:

1. **Build**: Use production Dockerfiles
2. **Test**: Run tests in containers
3. **Deploy**: Use `docker-compose.prod.yml`
4. **Scale**: Use management scripts

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Kafka Documentation](https://kafka.apache.org/documentation/)
- [RabbitMQ Documentation](https://www.rabbitmq.com/documentation.html)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
