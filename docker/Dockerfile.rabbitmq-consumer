# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY backend/package.json backend/pnpm-lock.yaml ./
COPY backend/.npmrc ./

# Install pnpm and dependencies
RUN npm install -g pnpm@10.12.1
RUN pnpm install --frozen-lockfile

# Copy source code
COPY backend/ .

# Generate Prisma client (if needed for API)
RUN pnpm prisma:generate

# Build the RabbitMQ Consumer application
RUN pnpm build:rabbitmq-consumer

# Production stage - lightweight image with built app
FROM node:18-alpine AS production

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@10.12.1

# Copy package files
COPY backend/package.json backend/pnpm-lock.yaml ./
COPY backend/.npmrc ./

# Copy Prisma schema first (needed for generation)
COPY --from=builder /app/prisma ./prisma

# Install only production dependencies
RUN pnpm install --prod --frozen-lockfile

# Generate Prisma client in production stage
RUN pnpm prisma generate

# Copy built application
COPY --from=builder /app/dist ./dist

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of the app directory
RUN chown -R nestjs:nodejs /app
USER nestjs

# The CMD will be provided by the container orchestrator (ECS, K8s, etc.)
# This allows flexibility in how the container is started
CMD ["node", "dist/apps/rabbitmq-consumer/main.js"]
