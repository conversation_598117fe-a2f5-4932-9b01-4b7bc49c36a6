import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  ProductServiceResponse,
  ListProductServiceParams,
  ListProductServiceResponse,
} from '../types/product-service';

export const useListProductService = ({
  page = 1,
  limit = 10,
  sortBy,
  sortOrder,
}: ListProductServiceParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: [
      'product-services',
      page,
      limit,
      sortBy,
      sortOrder,
    ],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListProductServiceResponse> => {
      return apiClient.get('/product-services', {
        params: {
          page,
          limit,
          sortBy,
          sortOrder,
        },
      });
    },
  });
};

export const useGetProductService = (id: string | null) => {
  return useQuery({
    queryKey: ['product-service', id],
    queryFn: (): Promise<ProductServiceResponse> => {
      return apiClient.get(`/product-services/${id || ''}`);
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });
};