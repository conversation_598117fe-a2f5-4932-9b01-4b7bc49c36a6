import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
  UpdateProductServiceRequest,
  ProductServiceResponse,
} from '../types/product-service';

/**
 * Hook for updating a product service
 */
export const useUpdateProductService = () => {
  return useMutation({
    mutationFn: async (data: { id: string } & UpdateProductServiceRequest): Promise<ProductServiceResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/product-services/${id}`, payload);
    },
  });
};