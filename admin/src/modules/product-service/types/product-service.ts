export interface ProductService {
  id: string;
  name: string;
  description?: string | null;
  icon?: string | null;
  identifier?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// API response structure for listing product services
export interface ListProductServiceResponse {
  success: boolean;
  message: string;
  data: ProductService[];
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  timestamp: number;
}

// API response structure for single product service
export interface ProductServiceResponse {
  success: boolean;
  message: string;
  data: ProductService;
  timestamp: number;
}

// Request for updating product service
export interface UpdateProductServiceRequest {
  name?: string;
  description?: string;
  icon?: string;
  identifier?: string;
}

// Parameters for listing product services with pagination
export interface ListProductServiceParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}