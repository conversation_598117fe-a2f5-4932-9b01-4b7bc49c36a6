'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Spinner } from '@/components/ui/spinner';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useFileUpload, formatBytes } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Upload, X, FileText, AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useUpdateProductService } from '../api/mutations';
import { useGetProductService } from '../api/queries';

const productServiceSchema = z.object({
   name: z
      .string()
      .min(1, 'Product service name is required')
      .min(2, 'Product service name must be at least 2 characters')
      .max(100, 'Product service name must not exceed 100 characters'),
   description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
});

type ProductServiceFormValues = z.infer<typeof productServiceSchema>;

interface ProductServiceModalProps {
   productServiceId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
}

export const ProductServiceModal = ({
   productServiceId,
   isOpen,
   onClose,
}: ProductServiceModalProps) => {
   const [fileError, setFileError] = useState<string>('');
   const updateProductServiceMutation = useUpdateProductService();
   const productServiceQuery = useGetProductService(productServiceId || null);
   const fileUploadMutation = useFileUploadMutation();
   const queryClient = useQueryClient();

   const form = useForm<ProductServiceFormValues>({
      resolver: zodResolver(productServiceSchema),
      defaultValues: {
         name: '',
         description: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Reset form when productServiceId changes or modal opens
   useEffect(() => {
      if (productServiceQuery.data?.data) {
         const productService = productServiceQuery.data.data;
         reset({
            name: productService.name,
            description: productService.description || '',
         });
      }
   }, [productServiceQuery.data, reset]);

   // Clear files and errors when modal opens
   useEffect(() => {
      if (isOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
      }
   }, [isOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
      }
   }, [file]);

   const onSubmit = async (data: ProductServiceFormValues) => {
      if (!productServiceId) return;

      setFileError('');

      try {
         let iconUrl: string | undefined;

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            iconUrl = uploadResponse.data.key;
         } else if (productServiceQuery.data?.data?.icon) {
            iconUrl = productServiceQuery.data.data.icon;
         }

         const payload: any = {
            name: data.name,
            description: data.description || undefined,
         };

         if (file) {
            payload.icon = iconUrl;
         }

         updateProductServiceMutation.mutate(
            { id: productServiceId, ...payload },
            {
               onSuccess: () => {
                  toast.success('Product service updated successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['product-services'] });
                  queryClient.invalidateQueries({
                     queryKey: ['product-service', productServiceId],
                  });
               },
               onError: (error: any) => {
                  toast.error(error?.message || 'Failed to update product service');
               },
            }
         );
      } catch (error: any) {
         console.error('Submit error:', error);
         setFileError('Failed to upload icon. Please try again.');
      }
   };

   const handleClose = () => {
      if (onClose) onClose();
      reset();
      clearFiles();
      clearErrors();
      setFileError('');
   };

   const isLoading = updateProductServiceMutation.isPending || fileUploadMutation.isPending;

   // Show loading state
   if (productServiceQuery.isLoading) {
      return (
         <Dialog open={isOpen} onOpenChange={() => handleClose()}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>
                     Please wait while we load the product service data.
                  </DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state
   if (productServiceQuery.error) {
      return (
         <Dialog open={isOpen} onOpenChange={() => handleClose()}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load product service data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load product service data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   return (
      <Dialog open={isOpen} onOpenChange={() => handleClose()}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-lg max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>Edit Product Service</DialogTitle>
               <DialogDescription>Update the product service information</DialogDescription>
            </DialogHeader>

            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='name'>Name *</Label>
                  <Controller
                     control={control}
                     name='name'
                     render={({ field }) => (
                        <Input
                           id='name'
                           placeholder='e.g. Ride Sharing'
                           {...field}
                           className='w-full'
                        />
                     )}
                  />
                  {errors.name && <ErrorMessage error={errors.name} />}
               </div>

               <div className='flex flex-col gap-2'>
                  <Label htmlFor='description'>Description</Label>
                  <Controller
                     control={control}
                     name='description'
                     render={({ field }) => (
                        <Textarea
                           id='description'
                           placeholder='e.g. On-demand ride sharing service'
                           {...field}
                           className='w-full'
                           rows={3}
                        />
                     )}
                  />
                  {errors.description && <ErrorMessage error={errors.description} />}
               </div>

               {/* Icon Upload Section */}
               <div className='space-y-4'>
                  <Label htmlFor='icon'>Product Service Icon</Label>

                  {/* File Upload Area */}
                  <div
                     role='button'
                     onClick={openFileDialog}
                     onDragEnter={handleDragEnter}
                     onDragLeave={handleDragLeave}
                     onDragOver={handleDragOver}
                     onDrop={handleDrop}
                     data-dragging={isDragging || undefined}
                     className='border-input cursor-pointer hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-32 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]'
                  >
                     <input
                        {...getInputProps()}
                        className='sr-only'
                        aria-label='Upload icon'
                        disabled={Boolean(file)}
                     />

                     <div className='flex flex-col items-center justify-center text-center'>
                        <div
                           className='bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border'
                           aria-hidden='true'
                        >
                           <Upload className='size-4 opacity-60' />
                        </div>
                        <p className='mb-1.5 text-sm font-medium'>Upload Service Icon</p>
                        <p className='text-muted-foreground text-xs'>
                           Drag & drop or click to browse (max. {formatBytes(maxSize)})
                        </p>
                        <p className='text-muted-foreground text-xs mt-1'>Supported: JPEG, PNG</p>
                     </div>
                  </div>

                  {/* Upload Errors */}
                  {uploadErrors.length > 0 && (
                     <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                        <AlertCircle className='size-3 shrink-0' />
                        <span>{uploadErrors[0]}</span>
                     </div>
                  )}
                  {fileError && (
                     <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                        <AlertCircle className='size-3 shrink-0' />
                        <span>{fileError}</span>
                     </div>
                  )}

                  {/* File Preview */}
                  {(file || productServiceQuery.data?.data?.icon) && (
                     <Card className='p-4'>
                        <div className='flex items-center justify-between'>
                           <div className='flex items-center gap-3'>
                              <div className='flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg'>
                                 <FileText className='w-5 h-5 text-blue-600' />
                              </div>
                              <div>
                                 {file ? (
                                    <>
                                       <p className='text-sm font-medium text-gray-900'>
                                          {file.file.name}
                                       </p>
                                       <p className='text-xs text-gray-500'>
                                          {formatBytes(file.file.size)}
                                       </p>
                                    </>
                                 ) : (
                                    <>
                                       <p className='text-sm font-medium text-gray-900'>
                                          Current Icon
                                       </p>
                                       <p className='text-xs text-gray-500'>Uploaded icon</p>
                                    </>
                                 )}
                              </div>
                           </div>
                           <div className='flex items-center gap-2'>
                              {!file && productServiceQuery.data?.data?.icon && (
                                 <Button
                                    type='button'
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                       window.open(productServiceQuery.data!.data!.icon!, '_blank')
                                    }
                                    className='text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300'
                                 >
                                    View
                                 </Button>
                              )}
                              {file && (
                                 <Button
                                    variant='ghost'
                                    size='sm'
                                    onClick={() => removeFile(file.id)}
                                    className='text-red-600 hover:text-red-700'
                                 >
                                    <X className='w-4 h-4' />
                                 </Button>
                              )}
                           </div>
                        </div>
                     </Card>
                  )}
               </div>

               <div className='flex gap-3 pt-4'>
                  <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                     Cancel
                  </Button>
                  <Button type='submit' disabled={isLoading} className='flex-1'>
                     {isLoading ? (
                        <>
                           Updating...
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : (
                        'Update Product Service'
                     )}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
};
