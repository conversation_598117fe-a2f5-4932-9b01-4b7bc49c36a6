'use client';

import { useState } from 'react';
import { useListLanguage } from '../api/queries';
import { LanguageModal } from '../components/language-modal';
import { LanguageTable } from '../components/language-table';

export function LanguagePage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);

   const listLanguage = useListLanguage({
      page,
      limit,
   });

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Languages</h2>
            <LanguageModal mode="create" />
         </div>

         <LanguageTable
            data={listLanguage.data}
            isLoading={listLanguage.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
         />
      </div>
   );
}