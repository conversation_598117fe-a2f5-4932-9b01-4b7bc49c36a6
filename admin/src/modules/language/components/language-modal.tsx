'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateLanguage, useUpdateLanguage } from '../api/mutations';
import { useGetLanguage } from '../api/queries';

const languageSchema = z.object({
   code: z
      .string()
      .min(1, 'Language code is required')
      .min(2, 'Language code must be at least 2 characters')
      .max(5, 'Language code must not exceed 5 characters')
      .regex(/^[a-zA-Z-]+$/, 'Language code can only contain letters and hyphens'),
   name: z
      .string()
      .min(1, 'Language name is required')
      .min(2, 'Language name must be at least 2 characters')
      .max(50, 'Language name must not exceed 50 characters')
      .regex(/^[a-zA-Z\s]+$/, 'Language name can only contain letters and spaces'),
   nameInNative: z
      .string()
      .min(1, 'Native name is required')
      .min(2, 'Native name must be at least 2 characters')
      .max(50, 'Native name must not exceed 50 characters')
      .regex(/^[^\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]+$/, 'Native name should not contain numbers or special symbols'),
});

type LanguageFormValues = z.infer<typeof languageSchema>;

interface LanguageModalProps {
   languageId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

export const LanguageModal = ({ languageId, isOpen, onClose, mode = 'create' }: LanguageModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const createLanguageMutation = useCreateLanguage();
   const updateLanguageMutation = useUpdateLanguage();
   const languageQuery = useGetLanguage(languageId || null);
   const queryClient = useQueryClient();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<LanguageFormValues>({
      resolver: zodResolver(languageSchema),
      defaultValues: {
         code: '',
         name: '',
         nameInNative: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   // Reset form when languageId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && languageQuery.data?.data) {
         const language = languageQuery.data.data;
         reset({
            code: language.code,
            name: language.name,
            nameInNative: language.nameInNative,
         });
      } else if (mode === 'create') {
         reset({
            code: '',
            name: '',
            nameInNative: '',
         });
      }
   }, [languageQuery.data, reset, mode]);

   const onSubmit = async (data: LanguageFormValues) => {
      if (mode === 'create') {
         createLanguageMutation.mutate(data, {
            onSuccess: () => {
               toast.success('Language created successfully');
               handleClose();
               queryClient.invalidateQueries({ queryKey: ['languages'] });
            },
         });
      } else if (mode === 'edit' && languageId) {
         updateLanguageMutation.mutate(
            { id: languageId, ...data },
            {
               onSuccess: () => {
                  toast.success('Language updated successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['languages'] });
                  queryClient.invalidateQueries({ queryKey: ['language', languageId] });
               },
            }
         );
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
   };

   const isLoading = mode === 'create' ? createLanguageMutation.isPending : updateLanguageMutation.isPending;

   // Show loading state for edit mode
   if (mode === 'edit' && languageQuery.isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>Please wait while we load the language data.</DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (mode === 'edit' && languageQuery.error) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load language data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load language data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   const content = (
      <DialogContent
         onInteractOutside={e => {
            e.preventDefault();
         }}
         className='max-w-md'
      >
         <DialogHeader>
            <DialogTitle>
               {mode === 'create' ? 'Create New Language' : 'Edit Language'}
            </DialogTitle>
            <DialogDescription>
               {mode === 'create' 
                  ? 'Add a new language to the system' 
                  : 'Update the language information'
               }
            </DialogDescription>
         </DialogHeader>

         <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
            <div className='flex flex-col gap-2'>
               <Label htmlFor='code'>Language Code *</Label>
               <Controller
                  control={control}
                  name='code'
                  render={({ field }) => (
                     <Input
                        id='code'
                        placeholder='e.g. en, es, fr'
                        {...field}
                        value={field.value.toLowerCase()}
                        onChange={(e) => field.onChange(e.target.value.toLowerCase())}
                        className='w-full'
                     />
                  )}
               />
               {errors.code && <ErrorMessage error={errors.code} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='name'>Language Name *</Label>
               <Controller
                  control={control}
                  name='name'
                  render={({ field }) => (
                     <Input
                        id='name'
                        placeholder='e.g. English, Spanish, French'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.name && <ErrorMessage error={errors.name} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='nameInNative'>Native Name *</Label>
               <Controller
                  control={control}
                  name='nameInNative'
                  render={({ field }) => (
                     <Input
                        id='nameInNative'
                        placeholder='e.g. English, Español, Français'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.nameInNative && <ErrorMessage error={errors.nameInNative} />}
            </div>

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {mode === 'create' ? 'Creating...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     mode === 'create' ? 'Create Language' : 'Update Language'
                  )}
               </Button>
            </div>
         </form>
      </DialogContent>
   );

   // For create mode, wrap with trigger
   if (mode === 'create' && isOpen === undefined) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogTrigger asChild>
               <Button className='cursor-pointer' variant='outline'>
                  <Plus />
                  Add Language
               </Button>
            </DialogTrigger>
            {content}
         </Dialog>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {content}
      </Dialog>
   );
};