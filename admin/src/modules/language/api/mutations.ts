import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
   CreateLanguageRequest,
   LanguageResponse,
   UpdateLanguageRequest,
} from '../types/language';

/**
 * Hook for creating a new language
 */
export const useCreateLanguage = () => {
   return useMutation({
      mutationFn: async (data: CreateLanguageRequest): Promise<LanguageResponse> => {
         return apiClient.post('/languages', data);
      },
   });
};

/**
 * Hook for updating a language
 */
export const useUpdateLanguage = () => {
   return useMutation({
      mutationFn: async (data: { id: string } & UpdateLanguageRequest): Promise<LanguageResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/languages/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a language
 */
export const useDeleteLanguage = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/languages/${id}`);
      },
   });
};