import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   LanguageResponse,
   ListLanguageParams,
   ListLanguageResponse,
} from '../types/language';

export const useListLanguage = ({
   page = 1,
   limit = 10,
   sortBy,
   sortOrder,
}: ListLanguageParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: [
         'languages',
         page,
         limit,
         sortBy,
         sortOrder,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListLanguageResponse> => {
         return apiClient.get('/languages', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
            },
         });
      },
   });
};

export const useGetLanguage = (id: string | null) => {
   return useQuery({
      queryKey: ['language', id],
      queryFn: (): Promise<LanguageResponse> => {
         return apiClient.get(`/languages/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};