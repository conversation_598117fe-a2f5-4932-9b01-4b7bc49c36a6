'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useForgotPassword, useVerifyForgotPasswordOtp } from '../api/mutations';
import { AuthHeader } from '../components/auth-header';
import { verifyOtpSchema, type VerifyOtpFormData } from '../schema/auth-schemas';

export default function VerifyOtpPage() {
   const [timeLeft, setTimeLeft] = useState(30);
   const [canResend, setCanResend] = useState(false);
   const router = useRouter();
   const searchParams = useSearchParams();
   const email = searchParams.get('email');

   const form = useForm<VerifyOtpFormData>({
      resolver: zodResolver(verifyOtpSchema),
      defaultValues: {
         otp: '',
      },
   });

   const {
      control,
      handleSubmit,
      formState: { errors },
      setValue,
      setFocus,
   } = form;

   const verifyOtpMutation = useVerifyForgotPasswordOtp();
   const resendOtpMutation = useForgotPassword();

   // Redirect if no email in params
   useEffect(() => {
      if (!email) {
         toast.error('Email not found. Please start from forgot password.');
         router.push('/auth/forgot-password');
      }
   }, [email, router]);

   // Countdown timer for resend
   useEffect(() => {
      if (timeLeft > 0) {
         const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
         return () => clearTimeout(timer);
      } else {
         setCanResend(true);
      }
   }, [timeLeft]);

   const onSubmit = async (data: VerifyOtpFormData) => {
      if (!email) return;

      const payload = {
         email,
         otp: data.otp,
      };

      verifyOtpMutation.mutate(payload, {
         onSuccess: () => {
            toast.success('OTP verified successfully');
            // Navigate to reset password with email and OTP in URL params
            router.push(
               `/auth/reset-password?email=${encodeURIComponent(email)}&otp=${encodeURIComponent(
                  data.otp
               )}`
            );
         },
      });
   };

   const handleResendOtp = () => {
      if (!canResend || !email) return;

      resendOtpMutation.mutate(
         { email },
         {
            onSuccess: () => {
               setValue('otp', '');
               setFocus('otp');
               toast.success('OTP resent successfully');
               setTimeLeft(30);
               setCanResend(false);
            },
         }
      );
   };

   const formatEmail = (email: string) => {
      const [localPart, domain] = email.split('@');
      if (localPart.length <= 3) return email;
      const maskedLocal = localPart.slice(0, 2) + '*'.repeat(localPart.length - 2);
      return `${maskedLocal}@${domain}`;
   };

   if (!email) {
      return null; // Will redirect in useEffect
   }

   return (
      <div className='bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10'>
         <div className='flex w-full max-w-sm flex-col gap-6'>
            <AuthHeader />

            <Card>
               <CardHeader className='text-center'>
                  <CardTitle className='text-xl'>Verify OTP</CardTitle>
                  <p className='text-sm text-muted-foreground'>
                     Enter the 4-digit code sent to{' '}
                     <span className='font-medium text-foreground'>{formatEmail(email)}</span>
                  </p>
               </CardHeader>
               <CardContent>
                  <Form {...form}>
                     <form onSubmit={handleSubmit(onSubmit)}>
                        <div className='grid gap-6'>
                           <div className='grid gap-3'>
                              <FormField
                                 control={control}
                                 name='otp'
                                 render={({ field }) => (
                                    <FormItem>
                                       <FormControl>
                                          <InputOTP maxLength={4} {...field}>
                                             <InputOTPGroup className='flex justify-center gap-2 w-full'>
                                                {[0, 1, 2, 3].map(index => (
                                                   <InputOTPSlot
                                                      key={index}
                                                      index={index}
                                                      className='rounded-md border border-input w-12 h-12'
                                                   />
                                                ))}
                                             </InputOTPGroup>
                                          </InputOTP>
                                       </FormControl>
                                    </FormItem>
                                 )}
                              />
                              <ErrorMessage error={errors.otp} />
                           </div>

                           <Button
                              type='submit'
                              className='w-full'
                              disabled={verifyOtpMutation.isPending}
                           >
                              {verifyOtpMutation.isPending ? (
                                 <>
                                    Verifying...
                                    <Spinner className='ml-2' />
                                 </>
                              ) : (
                                 'Verify OTP'
                              )}
                           </Button>

                           <div className='text-center space-y-3'>
                              <p className='text-sm text-muted-foreground'>
                                 Didn't receive the OTP?{' '}
                                 <Button
                                    type='button'
                                    variant='link'
                                    className='p-0 h-auto font-semibold'
                                    disabled={!canResend || resendOtpMutation.isPending}
                                    onClick={handleResendOtp}
                                 >
                                    {resendOtpMutation.isPending ? (
                                       <>
                                          Resending...
                                          <Spinner className='ml-2 h-3 w-3' />
                                       </>
                                    ) : canResend ? (
                                       'Resend OTP'
                                    ) : (
                                       `Resend in ${timeLeft}s`
                                    )}
                                 </Button>
                              </p>
                           </div>
                        </div>
                     </form>
                  </Form>
               </CardContent>
            </Card>

            <div className='text-center'>
               <Link
                  href='/auth/forgot-password'
                  className='flex items-center justify-center gap-2 text-sm text-primary hover:underline'
               >
                  <ArrowLeft className='h-4 w-4' />
                  Back to Forgot Password
               </Link>
            </div>
         </div>
      </div>
   );
}
