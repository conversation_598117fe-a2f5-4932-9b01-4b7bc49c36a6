'use client';

import { useState } from 'react';
import { useListVehicleCategory } from '../api/queries';
import { VehicleCategoryModal } from '../components/vehicle-category-modal';
import { VehicleCategoryTable } from '../components/vehicle-category-table';

export function VehicleCategoryPage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);

   const listVehicleCategory = useListVehicleCategory({
      page,
      limit,
   });

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Vehicle Categories</h2>
            <VehicleCategoryModal mode="create" />
         </div>

         <VehicleCategoryTable
            data={listVehicleCategory.data}
            isLoading={listVehicleCategory.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
         />
      </div>
   );
}