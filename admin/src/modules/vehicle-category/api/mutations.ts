import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
   CreateVehicleCategoryRequest,
   VehicleCategoryResponse,
   UpdateVehicleCategoryRequest,
} from '../types/vehicle-category';

/**
 * Hook for creating a new vehicle category
 */
export const useCreateVehicleCategory = () => {
   return useMutation({
      mutationFn: async (data: CreateVehicleCategoryRequest): Promise<VehicleCategoryResponse> => {
         return apiClient.post('/vehicle-type', data);
      },
   });
};

/**
 * Hook for updating a vehicle category
 */
export const useUpdateVehicleCategory = () => {
   return useMutation({
      mutationFn: async (data: { id: string } & UpdateVehicleCategoryRequest): Promise<VehicleCategoryResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/vehicle-type/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a vehicle category
 */
export const useDeleteVehicleCategory = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/vehicle-type/${id}`);
      },
   });
};