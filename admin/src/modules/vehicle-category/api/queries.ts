import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   VehicleCategoryResponse,
   ListVehicleCategoryParams,
   ListVehicleCategoryResponse,
} from '../types/vehicle-category';

export const useListVehicleCategory = ({
   page = 1,
   limit = 10,
   search,
   sortBy,
   sortOrder,
}: ListVehicleCategoryParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: [
         'vehicle-categories',
         page,
         limit,
         search,
         sortBy,
         sortOrder,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListVehicleCategoryResponse> => {
         return apiClient.get('/vehicle-type/paginate', {
            params: {
               page,
               limit,
               search,
               sortBy,
               sortOrder,
            },
         });
      },
   });
};

export const useGetVehicleCategory = (id: string | null) => {
   return useQuery({
      queryKey: ['vehicle-category', id],
      queryFn: (): Promise<VehicleCategoryResponse> => {
         return apiClient.get(`/vehicle-type/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};