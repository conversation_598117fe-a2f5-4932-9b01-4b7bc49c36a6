'use client';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { formatBytes, useFileUpload } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { AlertCircle, FileText, Upload, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useUpdateDriver } from '../api/mutations';

interface ProfilePictureModalProps {
   driverId: string;
   currentProfilePicture?: string | null;
   driverName: string;
   isOpen: boolean;
   onClose: () => void;
}

export const ProfilePictureModal = ({
   driverId,
   currentProfilePicture,
   driverName,
   isOpen,
   onClose,
}: ProfilePictureModalProps) => {
   const [fileError, setFileError] = useState<string>('');
   const [shouldRemoveImage, setShouldRemoveImage] = useState(false);
   const updateDriverMutation = useUpdateDriver();
   const fileUploadMutation = useFileUploadMutation();
   const queryClient = useQueryClient();

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Clear files and errors when modal opens
   useEffect(() => {
      if (isOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
         setShouldRemoveImage(false);
      }
   }, [isOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
         setShouldRemoveImage(false); // Reset remove flag when new file is selected
      }
   }, [file]);

   const onSubmit = async () => {
      setFileError('');

      try {
         let profilePictureUrl: string | null;

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            profilePictureUrl = uploadResponse.data.url;
         } else if (shouldRemoveImage) {
            profilePictureUrl = null;
         } else {
            // Keep existing image if no new file and not removing
            return; // No changes to make
         }

         updateDriverMutation.mutate(
            { id: driverId, profilePictureUrl },
            {
               onSuccess: () => {
                  const message = file
                     ? 'Profile picture updated successfully'
                     : shouldRemoveImage
                     ? 'Profile picture removed successfully'
                     : 'No changes made';
                  toast.success(message);
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
               },
               onError: (error: any) => {
                  console.error('Update error:', error);
                  toast.error('Failed to update profile picture');
               },
            }
         );
      } catch (error: any) {
         console.error('Submit error:', error);
         setFileError('Failed to upload image. Please try again.');
      }
   };

   const handleClose = () => {
      onClose();
      clearFiles();
      clearErrors();
      setFileError('');
      setShouldRemoveImage(false);
   };

   const isLoading = updateDriverMutation.isPending || fileUploadMutation.isPending;

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-md'
         >
            <DialogHeader>
               <DialogTitle>Update Profile Picture</DialogTitle>
               <DialogDescription>
                  Upload or remove profile picture for {driverName}
               </DialogDescription>
            </DialogHeader>

            <div className='space-y-4 py-4'>
               {/* File Upload Area */}
               <div className='space-y-2'>
                  <label className='text-sm font-medium text-gray-900'>Profile Picture</label>
                  <div
                     role='button'
                     onClick={openFileDialog}
                     onDragEnter={handleDragEnter}
                     onDragLeave={handleDragLeave}
                     onDragOver={handleDragOver}
                     onDrop={handleDrop}
                     data-dragging={isDragging || undefined}
                     className='border-input cursor-pointer hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-32 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]'
                  >
                     <input
                        {...getInputProps()}
                        className='sr-only'
                        aria-label='Upload image'
                        disabled={Boolean(file)}
                     />

                     <div className='flex flex-col items-center justify-center text-center'>
                        <div
                           className='bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border'
                           aria-hidden='true'
                        >
                           <Upload className='size-4 opacity-60' />
                        </div>
                        <p className='mb-1.5 text-sm font-medium'>Upload Profile Picture</p>
                        <p className='text-muted-foreground text-xs'>
                           Drag & drop or click to browse (max. {formatBytes(maxSize)})
                        </p>
                        <p className='text-muted-foreground text-xs mt-1'>Supported: JPEG, PNG</p>
                     </div>
                  </div>
               </div>

               {/* Upload Errors */}
               {uploadErrors.length > 0 && (
                  <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                     <AlertCircle className='size-3 shrink-0' />
                     <span>{uploadErrors[0]}</span>
                  </div>
               )}
               {fileError && (
                  <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                     <AlertCircle className='size-3 shrink-0' />
                     <span>{fileError}</span>
                  </div>
               )}

               {/* Current Picture or File Preview */}
               {(currentProfilePicture && !shouldRemoveImage) || file ? (
                  <Card className='p-4'>
                     <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-3'>
                           <div className='flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg'>
                              <FileText className='w-5 h-5 text-blue-600' />
                           </div>
                           <div>
                              {file ? (
                                 <>
                                    <p className='text-sm font-medium text-gray-900'>
                                       {file.file.name}
                                    </p>
                                    <p className='text-xs text-gray-500'>
                                       {formatBytes(file.file.size)}
                                    </p>
                                 </>
                              ) : (
                                 <>
                                    <p className='text-sm font-medium text-gray-900'>
                                       Current Image
                                    </p>
                                    <p className='text-xs text-gray-500'>Uploaded image</p>
                                 </>
                              )}
                           </div>
                        </div>
                        <div className='flex items-center gap-2'>
                           {!file && currentProfilePicture && (
                              <Button
                                 type='button'
                                 variant='outline'
                                 size='sm'
                                 onClick={() => window.open(currentProfilePicture, '_blank')}
                                 className='text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300'
                              >
                                 View
                              </Button>
                           )}
                           <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => {
                                 if (file) {
                                    removeFile(file.id);
                                 } else {
                                    // Mark current image for removal
                                    setShouldRemoveImage(true);
                                 }
                              }}
                              className='text-red-600 hover:text-red-700'
                           >
                              <X className='w-4 h-4' />
                           </Button>
                        </div>
                     </div>
                  </Card>
               ) : null}

               {shouldRemoveImage && !file && (
                  <div className='text-sm text-gray-600 bg-red-50 border border-red-200 rounded-lg p-3'>
                     Current image will be removed when you save.
                  </div>
               )}
            </div>

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='button' onClick={onSubmit} disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {file ? 'Uploading...' : shouldRemoveImage ? 'Removing...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : file ? (
                     'Update Picture'
                  ) : shouldRemoveImage ? (
                     'Remove Picture'
                  ) : (
                     'Save'
                  )}
               </Button>
            </div>
         </DialogContent>
      </Dialog>
   );
};