'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { useApproveKycDocument } from '../api/mutations';
import { toast } from '@/lib/toast';
import { CheckCircle } from 'lucide-react';

interface ApproveKycModalProps {
   open: boolean;
   onClose: () => void;
   onSuccess: () => void;
   documentId: string;
   documentName: string;
}

export function ApproveKycModal({
   open,
   onClose,
   onSuccess,
   documentId,
   documentName,
}: ApproveKycModalProps) {
   const approveKycMutation = useApproveKycDocument();

   const handleApprove = async () => {
      try {
         await approveKycMutation.mutateAsync(documentId);
         toast.success('Document approved successfully');
         onSuccess();
         onClose();
      } catch {
         toast.error('Failed to approve document');
      }
   };

   return (
      <Dialog open={open} onOpenChange={onClose}>
         <DialogContent 
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className="sm:max-w-md"
         >
            <DialogHeader className="text-center">
               <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                  <CheckCircle className="h-6 w-6 text-green-600" />
               </div>
               <DialogTitle className="text-lg font-semibold text-gray-900">
                  Approve Document
               </DialogTitle>
               <DialogDescription className="text-sm text-gray-600">
                  Are you sure you want to approve the {documentName}? This action will mark the document as verified.
               </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2">
               <Button
                  variant="outline"
                  onClick={onClose}
                  disabled={approveKycMutation.isPending}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleApprove}
                  disabled={approveKycMutation.isPending}
                  className="bg-green-600 text-white hover:bg-green-700"
               >
                  {approveKycMutation.isPending ? 'Approving...' : 'Approve Document'}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}
