'use client';
import {
   <PERSON><PERSON>,
   DialogContent,
   DialogDescription,
   DialogFooter,
   Di<PERSON>Header,
   DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle } from 'lucide-react';
import { useUpdateDriverStatus } from '../api/mutations';
import { toast } from 'sonner';

interface DriverStatusModalProps {
   isOpen: boolean;
   onClose: () => void;
   driverId: string;
   driverName: string;
   currentStatus: 'active' | 'inactive' | 'pending' | 'suspended' | undefined;
   newStatus: 'active' | 'inactive';
   onStatusUpdated: () => void;
}

export function DriverStatusModal({
   isOpen,
   onClose,
   driverId,
   driverName,
   currentStatus: _currentStatus,
   newStatus,
   onStatusUpdated,
}: DriverStatusModalProps) {
   const updateDriverStatusMutation = useUpdateDriverStatus();

   const handleConfirm = async () => {
      try {
         await updateDriverStatusMutation.mutateAsync({
            id: driverId,
            status: newStatus,
         });
         toast.success(`Driver ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
         onStatusUpdated();
         onClose();
      } catch (error: any) {
         toast.error(error?.response?.data?.message || 'Failed to update driver status');
      }
   };

   const isActivating = newStatus === 'active';
   const actionText = isActivating ? 'activate' : 'deactivate';
   const actionTextCapitalized = isActivating ? 'Activate' : 'Deactivate';

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-md'>
            <DialogHeader>
               <div className='flex items-center gap-3 mb-2'>
                  {isActivating ? (
                     <div className='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center'>
                        <CheckCircle className='w-5 h-5 text-green-600' />
                     </div>
                  ) : (
                     <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
                        <AlertTriangle className='w-5 h-5 text-red-600' />
                     </div>
                  )}
                  <DialogTitle className='text-lg font-semibold'>
                     {actionTextCapitalized} Driver
                  </DialogTitle>
               </div>
               <DialogDescription className='text-base text-gray-600'>
                  Are you sure you want to {actionText} <span className='font-medium'>{driverName}</span>?
               </DialogDescription>
               {isActivating && (
                  <div className='mt-3 p-3 bg-blue-50 rounded-lg'>
                     <div className='text-sm text-blue-800'>
                        <strong>Note:</strong> Please check before activating the driver that all mandatory KYC documents are approved and at least one vehicle is verified. The system will validate these requirements during activation.
                     </div>
                  </div>
               )}
               {!isActivating && (
                  <div className='mt-3 p-3 bg-red-50 rounded-lg'>
                     <div className='text-sm text-red-800'>
                        <strong>Warning:</strong> Deactivating will prevent the driver from accepting new ride requests.
                     </div>
                  </div>
               )}
            </DialogHeader>
            <DialogFooter className='flex gap-3 sm:gap-3'>
               <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={updateDriverStatusMutation.isPending}
                  className='flex-1'
               >
                  Cancel
               </Button>
               <Button
                  type='button'
                  onClick={handleConfirm}
                  disabled={updateDriverStatusMutation.isPending}
                  className={`flex-1 ${
                     isActivating
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-red-600 hover:bg-red-700 text-white'
                  }`}
               >
                  {updateDriverStatusMutation.isPending
                     ? 'Processing...'
                     : `${actionTextCapitalized} Driver`}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}