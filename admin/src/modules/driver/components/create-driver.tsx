'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';

import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon, Plus } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import * as z from 'zod';
import { useCreateDriver } from '../api/mutations';
import { useCities, useLanguages } from '../api/queries';
import { useQueryClient } from '@tanstack/react-query';
import { DriverRegistration } from './driver-registration';
import { DriverOtpVerification } from './driver-otp-verification';
import { formatDateForAPI } from '../utils/driver-utils';

// Define the Zod schema for driver profile completion
const driverProfileSchema = z.object({
   firstName: z
      .string()
      .min(1, 'First name is required')
      .min(2, 'First name must be at least 2 characters')
      .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters'),
   lastName: z
      .string()
      .min(1, 'Last name is required')
      .min(2, 'Last name must be at least 2 characters')
      .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters'),
   gender: z.enum(['MALE', 'FEMALE', 'OTHER'], {
      message: 'Gender is required',
   }),
   dob: z.date({
      message: 'Date of birth is required',
   }).refine((date) => {
      const today = new Date();
      const birthDate = new Date(date);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      const dayDiff = today.getDate() - birthDate.getDate();
      
      // Calculate exact age
      const exactAge = age - (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0) ? 1 : 0);
      
      return exactAge >= 18;
   }, {
      message: 'Driver must be at least 18 years old',
   }),
   email: z.string().min(1, 'Email is required').email('Please enter a valid email address'),
   cityId: z.string().min(1, 'City is required'),
   languageId: z.string().min(1, 'Language is required'),
});

// Infer the type from the schema
type DriverProfileFormValues = z.infer<typeof driverProfileSchema>;

// Multi-step flow states
type FlowStep = 'registration' | 'otp-verification' | 'profile-completion';

export const CreateDriver = () => {
   const [open, setOpen] = useState(false);
   const [dobOpen, setDobOpen] = useState(false);
   const [selectKey, setSelectKey] = useState(0);
   const [currentStep, setCurrentStep] = useState<FlowStep>('registration');
   const [phoneNumber, setPhoneNumber] = useState<string>('');
   const [userId, setUserId] = useState<string>('');

   const createDriverMutation = useCreateDriver();
   const citiesQuery = useCities();
   const languagesQuery = useLanguages();
   const queryClient = useQueryClient();
   const searchParams = useSearchParams();
   const router = useRouter();

   // Calculate default date (18 years ago)
   const getDefaultDate = () => {
      const today = new Date();
      const eighteenYearsAgo = new Date(
         today.getFullYear() - 18,
         today.getMonth(),
         today.getDate()
      );
      return eighteenYearsAgo;
   };

   // Form for profile completion step
   const form = useForm<DriverProfileFormValues>({
      resolver: zodResolver(driverProfileSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         gender: 'MALE',
         dob: getDefaultDate(),
         email: '',
         cityId: '',
         languageId: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   // URL state management
   useEffect(() => {
      const phoneFromUrl = searchParams.get('phone');
      const stepFromUrl = searchParams.get('step') as FlowStep;

      if (phoneFromUrl) {
         setPhoneNumber(phoneFromUrl);
         if (
            stepFromUrl &&
            ['registration', 'otp-verification', 'profile-completion'].includes(stepFromUrl)
         ) {
            setCurrentStep(stepFromUrl);
         }
      }
   }, [searchParams]);

   // Update URL when state changes
   const updateUrlState = (step: FlowStep, phone?: string) => {
      const params = new URLSearchParams();
      if (phone) params.set('phone', phone);
      params.set('step', step);
      router.replace(`?${params.toString()}`);
   };

   // Handle successful phone registration
   const handleRegistrationSuccess = (phone: string, userIdFromRegistration: string) => {
      setPhoneNumber(phone);
      setUserId(userIdFromRegistration);
      setCurrentStep('otp-verification');
      updateUrlState('otp-verification', phone);
   };

   // Handle successful OTP verification
   const handleOtpSuccess = async (phone: string) => {
      // Just move to profile completion step - don't auto-create profile yet
      setCurrentStep('profile-completion');
      updateUrlState('profile-completion', phone);
      toast.success('Phone verified! Please complete your profile.');
   };

   // Handle profile completion
   const onSubmit = async (data: DriverProfileFormValues) => {
      if (!userId) {
         toast.error('User ID not found. Please restart the registration process.');
         return;
      }

      if (!phoneNumber) {
         toast.error('Phone number not found. Please restart the registration process.');
         return;
      }

      createDriverMutation.mutate(
         {
            userId: userId,
            firstName: data.firstName,
            lastName: data.lastName,
            mobileNumber: phoneNumber, // Use mobileNumber as expected by backend
            email: data.email,
            gender: data.gender,
            dob: formatDateForAPI(data.dob),
            cityId: data.cityId,
            languageId: data.languageId,
         },
         {
            onSuccess: () => {
               toast.success('Driver profile created successfully');
               handleClose();
               queryClient.invalidateQueries({ queryKey: ['drivers'] });
            },
         }
      );
   };

   // Handle sheet close
   const handleClose = () => {
      setOpen(false);
      setCurrentStep('registration');
      setPhoneNumber('');
      setUserId('');
      reset();
      // Force re-render of Select components
      setSelectKey(prev => prev + 1);
      if (typeof window !== 'undefined') {
         router.replace(window.location.pathname); // Clear URL params
      }
   };

   // Handle back navigation
   const handleBack = () => {
      if (currentStep === 'otp-verification') {
         setCurrentStep('registration');
         updateUrlState('registration', phoneNumber);
      } else if (currentStep === 'profile-completion') {
         setCurrentStep('otp-verification');
         updateUrlState('otp-verification', phoneNumber);
      }
   };

   // Render step content
   const renderStepContent = () => {
      switch (currentStep) {
         case 'registration':
            return (
               <DriverRegistration onSuccess={handleRegistrationSuccess} onCancel={handleClose} />
            );
         case 'otp-verification':
            return (
               <DriverOtpVerification
                  phoneNumber={phoneNumber}
                  onSuccess={handleOtpSuccess}
                  onBack={handleBack}
               />
            );
         case 'profile-completion':
            return renderProfileForm();
         default:
            return null;
      }
   };

   // Render profile completion form
   const renderProfileForm = () => (
      <div className='space-y-6'>

         <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='firstName'>First Name *</Label>
                  <Controller
                     control={control}
                     name='firstName'
                     render={({ field }) => (
                        <Input
                           id='firstName'
                           placeholder='Enter first name'
                           {...field}
                           className='w-full'
                        />
                     )}
                  />
                  {errors.firstName && <ErrorMessage error={errors.firstName} />}
               </div>

               <div className='flex flex-col gap-2'>
                  <Label htmlFor='lastName'>Last Name *</Label>
                  <Controller
                     control={control}
                     name='lastName'
                     render={({ field }) => (
                        <Input
                           id='lastName'
                           placeholder='Enter last name'
                           {...field}
                           className='w-full'
                        />
                     )}
                  />
                  {errors.lastName && <ErrorMessage error={errors.lastName} />}
               </div>
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='gender'>Gender *</Label>
               <Controller
                  control={control}
                  name='gender'
                  render={({ field }) => (
                     <Select key={`gender-${selectKey}`} onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='Select gender' />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value='MALE'>Male</SelectItem>
                           <SelectItem value='FEMALE'>Female</SelectItem>
                           <SelectItem value='OTHER'>Other</SelectItem>
                        </SelectContent>
                     </Select>
                  )}
               />
               {errors.gender && <ErrorMessage error={errors.gender} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='dob'>Date of Birth *</Label>
               <Controller
                  control={control}
                  name='dob'
                  render={({ field }) => {
                     return (
                        <Popover modal={true} open={dobOpen} onOpenChange={setDobOpen}>
                           <PopoverTrigger asChild>
                              <Button
                                 variant={'outline'}
                                 className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground'
                                 )}
                              >
                                 {field.value ? (
                                    format(field.value, 'dd/MM/yyyy')
                                 ) : (
                                    <span>dd/mm/yyyy</span>
                                 )}
                                 <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                              </Button>
                           </PopoverTrigger>
                           <PopoverContent className='w-auto p-0 z-[9999]' align='start'>
                              <Calendar
                                 mode='single'
                                 selected={field.value}
                                 onSelect={date => {
                                    field.onChange(date);
                                    setDobOpen(false);
                                 }}
                                 captionLayout='dropdown'
                                 defaultMonth={field.value || getDefaultDate()}
                                 disabled={(date) => {
                                    const today = new Date();
                                    const eighteenYearsAgo = new Date(
                                       today.getFullYear() - 18,
                                       today.getMonth(),
                                       today.getDate()
                                    );
                                    return date > eighteenYearsAgo;
                                 }}
                                 toYear={new Date().getFullYear() - 18}
                                 fromYear={1900}
                              />
                           </PopoverContent>
                        </Popover>
                     );
                  }}
               />
               {errors.dob && <ErrorMessage error={errors.dob} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='email'>Email *</Label>
               <Controller
                  control={control}
                  name='email'
                  render={({ field }) => (
                     <Input
                        id='email'
                        type='email'
                        placeholder='Enter email address'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.email && <ErrorMessage error={errors.email} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='cityId'>City *</Label>
               <Controller
                  control={control}
                  name='cityId'
                  render={({ field }) => (
                     <Select key={`city-${selectKey}`} onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='Select city' />
                        </SelectTrigger>
                        <SelectContent>
                           {citiesQuery.data?.data?.map(city => (
                              <SelectItem key={city.id} value={city.id}>
                                 {city.name}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  )}
               />
               {errors.cityId && <ErrorMessage error={errors.cityId} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='languageId'>Language *</Label>
               <Controller
                  control={control}
                  name='languageId'
                  render={({ field }) => (
                     <Select key={`language-${selectKey}`} onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='Select language' />
                        </SelectTrigger>
                        <SelectContent>
                           {languagesQuery.data?.data?.map(language => (
                              <SelectItem key={language.id} value={language.id}>
                                 {language.name}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  )}
               />
               {errors.languageId && <ErrorMessage error={errors.languageId} />}
            </div>

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleBack} className='flex-1'>
                  Back
               </Button>
               <Button type='submit' disabled={createDriverMutation.isPending} className='flex-1'>
                  {createDriverMutation.isPending ? (
                     <>
                        Creating Profile...
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     'Complete Profile'
                  )}
               </Button>
            </div>
         </form>
      </div>
   );

   return (
      <Dialog
         open={open}
         onOpenChange={(isOpen: boolean) => {
            if (!isOpen) {
               handleClose();
            } else {
               setOpen(true);
            }
         }}
      >
         <DialogTrigger asChild>
            <Button className='cursor-pointer' id='create-driver-trigger' variant='outline'>
               <Plus />
               Add Driver
            </Button>
         </DialogTrigger>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-md max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>
                  {currentStep === 'registration' && 'Register New Driver'}
                  {currentStep === 'otp-verification' && 'Verify Phone Number'}
                  {currentStep === 'profile-completion' && 'Complete Driver Profile'}
               </DialogTitle>
               <DialogDescription>
                  {currentStep === 'registration' && 'Enter phone number to send OTP'}
                  {currentStep === 'otp-verification' && 'Enter the OTP sent to your phone'}
                  {currentStep === 'profile-completion' && 'Fill in the driver details'}
               </DialogDescription>
            </DialogHeader>

            <div className='py-4'>{renderStepContent()}</div>
         </DialogContent>
      </Dialog>
   );
};
