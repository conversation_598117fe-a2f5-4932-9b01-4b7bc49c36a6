'use client';

import React from 'react';
import { FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DriverVehicle } from '../types/driver';
import { VehicleDocuments } from './vehicle-documents';

interface VehicleDocumentsModalProps {
   isOpen: boolean;
   onClose: () => void;
   vehicle: DriverVehicle;
}

export function VehicleDocumentsModal({
   isOpen,
   onClose,
   vehicle,
}: VehicleDocumentsModalProps) {
   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className="sm:max-w-2xl"
         >
            <DialogHeader>
               <DialogTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Vehicle Documents - {vehicle.vehicleNumber}
               </DialogTitle>
            </DialogHeader>

            <div className="max-h-[70vh] overflow-y-auto py-4">
               <VehicleDocuments vehicle={vehicle} />
            </div>

            <div className="flex justify-end pt-4 border-t">
               <Button onClick={onClose}>
                  Done
               </Button>
            </div>
         </DialogContent>
      </Dialog>
   );
}