'use client';

import { ErrorBoundary } from 'react-error-boundary';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, ArrowLeft, Mail, MapPin, Phone, User, Pen } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { useGetDriver } from '../api/queries';
import { DriverPersonalDetails } from '../components/driver-personal-details';
import { DriverKycDocuments } from '../components/driver-kyc-documents';
import { DriverVehicles } from '../components/driver-vehicles';
import { ProfilePictureModal } from '../components/profile-picture-modal';
import { DriverStatusModal } from '../components/driver-status-modal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useParams } from 'next/navigation';

export function DriverDetailsPage() {
   const params = useParams<{ id: string }>();
   const driverId = params.id;
   const [activeTab, setActiveTab] = useState('personal');
   const [isProfilePictureModalOpen, setIsProfilePictureModalOpen] = useState(false);
   const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
   const [pendingStatus, setPendingStatus] = useState<'active' | 'inactive'>('active');
   const { data: driver, isLoading, error, refetch } = useGetDriver(driverId);

   const handleStatusButtonClick = (newStatus: 'active' | 'inactive') => {
      setPendingStatus(newStatus);
      setIsStatusModalOpen(true);
   };

   const handleStatusUpdated = () => {
      refetch();
   };

   if (isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='animate-pulse'>
               <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
               <div className='h-32 bg-gray-200 rounded mb-4'></div>
               <div className='h-96 bg-gray-200 rounded'></div>
            </div>
         </div>
      );
   }

   if (error || !driver?.data) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='flex items-center gap-4 mb-6'>
               <Link href='/dashboard/drivers'>
                  <Button variant='ghost' size='sm' className='gap-2'>
                     <ArrowLeft className='h-4 w-4' />
                     Back to Drivers
                  </Button>
               </Link>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>Driver Not Found</h3>
               <p className='text-gray-600'>
                  The driver you're looking for doesn't exist or has been removed.
               </p>
            </Card>
         </div>
      );
   }

   const driverData = driver.data;
   const fullName = `${driverData.firstName || ''} ${driverData.lastName || ''}`.trim() || 'N/A';

   return (
      <div className='flex flex-1 flex-col space-y-6 px-6 py-6'>
         {/* Clean Header */}
         <div className='flex items-center gap-3'>
            <Link href='/dashboard/drivers'>
               <Button
                  variant='ghost'
                  size='sm'
                  className='gap-2 text-gray-600 hover:text-gray-900'
               >
                  <ArrowLeft className='h-4 w-4' />
                  Back to Drivers
               </Button>
            </Link>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-600'>Drivers</span>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-600'>Driver Details</span>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-900 font-medium'>{fullName}</span>
         </div>

         {/* Compact Driver Header */}
         <Card className='p-6'>
            <div className='flex items-center gap-4'>
               {/* Profile Picture with Edit Button */}
               <div className='flex-shrink-0 relative'>
                  <div 
                     className='cursor-pointer'
                     onClick={() => setIsProfilePictureModalOpen(true)}
                  >
                     {driverData.profilePictureUrl ? (
                        <ErrorBoundary
                           fallback={
                              <div className='w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center'>
                                 <User className='w-6 h-6 text-gray-400' />
                              </div>
                           }
                        >
                           <Image
                              src={driverData.profilePictureUrl}
                              alt={fullName}
                              width={56}
                              height={56}
                              className='w-14 h-14 rounded-full object-cover hover:opacity-80 transition-opacity'
                           />
                        </ErrorBoundary>
                     ) : (
                        <div className='w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors'>
                           <User className='w-6 h-6 text-gray-400' />
                        </div>
                     )}
                  </div>
                  <Button
                     variant='ghost'
                     size='sm'
                     onClick={() => setIsProfilePictureModalOpen(true)}
                     className='absolute -bottom-1 -right-1 h-7 w-7 p-2 rounded-full bg-white border shadow-sm hover:bg-gray-50'
                  >
                     <Pen className='h-1 w-1 scale-75' />
                  </Button>
               </div>

               {/* Clean Driver Info */}
               <div className='flex-1 min-w-0'>
                  <div className='flex items-center gap-3 mb-2'>
                     <h1 className='text-xl font-semibold text-gray-900 truncate'>{fullName}</h1>
                     <Badge
                        variant={driverData.phoneVerified ? 'default' : 'secondary'}
                        className={`text-xs ${
                           driverData.phoneVerified
                              ? 'bg-green-100 text-green-700'
                              : 'bg-gray-100 text-gray-700'
                        }`}
                     >
                        {driverData.phoneVerified ? 'Verified' : 'Unverified'}
                     </Badge>
                     <Badge
                        variant='secondary'
                        className={`text-xs ${
                           driverData.status === 'active'
                              ? 'bg-green-100 text-green-700'
                              : driverData.status === 'inactive'
                              ? 'bg-red-100 text-red-700'
                              : 'bg-yellow-100 text-yellow-700'
                        }`}
                     >
                        {driverData.status === 'active' ? 'Active' : 
                         driverData.status === 'inactive' ? 'Inactive' : 
                         'Pending'}
                     </Badge>
                  </div>

                  <div className='flex items-center gap-6 text-sm text-gray-600'>
                     <div className='flex items-center gap-2'>
                        <Mail className='w-4 h-4 text-gray-400' />
                        <span className='truncate'>{driverData.email || 'No email'}</span>
                     </div>
                     <div className='flex items-center gap-2'>
                        <Phone className='w-4 h-4 text-gray-400' />
                        <span>{driverData.phoneNumber}</span>
                     </div>
                     <div className='flex items-center gap-2'>
                        <MapPin className='w-4 h-4 text-gray-400' />
                        <span>{driverData.cityName || 'Not specified'}</span>
                     </div>
                  </div>
               </div>

               {/* Status Update Button */}
               <div className='flex-shrink-0'>
                  <Button
                     onClick={() => handleStatusButtonClick(driverData.status === 'active' ? 'inactive' : 'active')}
                     variant='outline'
                     size='sm'
                     className={`${
                        driverData.status === 'active' 
                           ? 'border border-gray-300 bg-white text-gray-600 hover:text-red-600 hover:border-red-400' 
                           : 'border border-green-400 bg-white text-green-600 hover:text-green-700 hover:border-green-500'
                     } text-sm font-medium rounded-md px-3 py-1 transition-colors`}
                  >
                     {driverData.status === 'active' ? 'Deactivate' : 'Activate'}
                  </Button>
               </div>
            </div>
         </Card>

         {/* Clean Tabs */}
         <Card>
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
               <div className='border-b border-gray-200 px-6 pt-4 pb-0'>
                  <TabsList className='grid w-full grid-cols-3 max-w-lg bg-gray-50'>
                     <TabsTrigger value='personal' className='text-sm'>
                        Personal Details
                     </TabsTrigger>
                     <TabsTrigger value='kyc' className='text-sm'>
                        KYC
                     </TabsTrigger>
                     <TabsTrigger value='vehicles' className='text-sm'>
                        Vehicles
                     </TabsTrigger>
                  </TabsList>
               </div>

               <div className='p-6'>
                  <TabsContent value='personal' className='mt-0'>
                     <DriverPersonalDetails driver={driverData} />
                  </TabsContent>

                  <TabsContent value='kyc' className='mt-0'>
                     <DriverKycDocuments driverId={driverId} />
                  </TabsContent>

                  <TabsContent value='vehicles' className='mt-0'>
                     <DriverVehicles profileId={driverData.id} />
                  </TabsContent>
               </div>
            </Tabs>
         </Card>

         {/* Profile Picture Modal */}
         <ProfilePictureModal
            driverId={driverId}
            currentProfilePicture={driverData.profilePictureUrl}
            driverName={fullName}
            isOpen={isProfilePictureModalOpen}
            onClose={() => setIsProfilePictureModalOpen(false)}
         />

         {/* Driver Status Modal */}
         <DriverStatusModal
            isOpen={isStatusModalOpen}
            onClose={() => setIsStatusModalOpen(false)}
            driverId={driverId}
            driverName={fullName}
            currentStatus={driverData.status}
            newStatus={pendingStatus}
            onStatusUpdated={handleStatusUpdated}
         />
      </div>
   );
}
