// City interface for API responses
export interface City {
   id: string;
   name: string;
   countryId: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// Language interface for API responses
export interface Language {
   id: string;
   code: string;
   name: string;
   nameInNative: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// Driver registration and OTP types
export interface DriverRegistrationRequest {
   phoneNumber: string;
}

export interface DriverRegistrationResponse {
   success: boolean;
   message: string;
   data: {
      userId: string;
      phoneNumber: string;
      message: string;
   };
   timestamp: number;
}

export interface DriverOtpVerificationRequest {
   phoneNumber: string;
   otp: string;
}

export interface DriverResendOtpRequest {
   phoneNumber: string;
}

// Updated Driver interface to match actual API response structure
export interface Driver {
   id: string;
   userId: string;
   roleId: string;
   firstName: string | null;
   lastName: string | null;
   email: string | null;
   phoneNumber: string; // API uses phoneNumber, not mobileNumber
   gender: 'MALE' | 'FEMALE' | 'OTHER' | null;
   dob: string | null;
   profilePictureUrl: string | null;
   cityId: string | null;
   cityName?: string; // API includes cityName for display
   languageId: string | null;
   referralCode: string | null;
   phoneVerified: boolean;
   emailVerified: boolean;
   status?: 'active' | 'pending' | 'suspended' | 'inactive'; // Account status
   onboarding?: {
      currentStep: string;
   }; // Onboarding status
   createdAt: string;
   updatedAt: string;
}

// API response structure for listing drivers
export interface ListDriverResponse {
   success: boolean;
   message: string;
   data: Driver[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
   };
   timestamp: number;
}

// API response structure for single driver
export interface DriverResponse {
   success: boolean;
   message: string;
   data: Driver;
   timestamp: number;
}

// Request for creating driver profile (after OTP verification)
export interface CreateDriverRequest {
   userId: string; // Required field
   firstName: string; // Required field
   lastName: string; // Required field
   mobileNumber: string; // Required field - API uses mobileNumber, not phoneNumber
   email: string; // Required field
   gender: 'MALE' | 'FEMALE' | 'OTHER'; // Required field
   dob: string; // Required field - ISO 8601 date string
   profilePictureUrl?: string; // Optional field
   cityId: string; // Required field - UUID
   languageId: string; // Required field - UUID
}

// Request for updating driver profile
export interface UpdateDriverRequest {
   firstName?: string;
   lastName?: string;
   gender?: 'MALE' | 'FEMALE' | 'OTHER';
   dob?: string;
   profilePictureUrl?: string | null;
   cityId?: string;
   languageId?: string;
}

// Parameters for listing drivers with filters
export interface ListDriverParams {
   page?: number;
   limit?: number;
   sortBy?: string;
   sortOrder?: 'asc' | 'desc';
   search?: string;
   cityId?: string;
   name?: string;
   email?: string;
   phoneNumber?: string;
   status?: string;
   vehicleTypeId?: string;
}

export interface DriverFilters {
   page?: number;
   perPage?: number;
   search?: string;
   status?: 'pending' | 'active' | 'inactive';
   location?: string;
}


// KYC Document Types
export interface KycDocument {
   id: string;
   countryId: string;
   name: string;
   identifier: string;
   requiredFields: {
      fields: string[];
   };
   isMandatory: boolean;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
   driverKyc: DriverKyc | null;
}

export interface DriverKyc {
   id: string;
   userProfileId: string;
   kycDocumentId: string;
   documentNumber: string | null;
   documentUrl: string;
   documentFields: Record<string, any> | null;
   expiryDate: string | null;
   fromDigilocker: boolean;
   status: 'PENDING' | 'APPROVED' | 'REJECTED';
   rejectionNote: string | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

export interface KycDocumentsResponse {
   success: boolean;
   message: string;
   data: KycDocument[];
   timestamp: number;
}

export interface AddKycDocumentRequest {
   userProfileId: string;
   documentUrl: string;
   fromDigilocker: boolean;
   kycDocumentId: string;
   documentNumber?: string;
   expiryDate?: string;
   documentFields?: Record<string, any>;
}

export interface AddKycDocumentResponse {
   success: boolean;
   message: string;
   data: DriverKyc;
   timestamp: number;
}

// Vehicle Types
export interface VehicleType {
   id: string;
   name: string;
   description: string | null;
   image: string | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

export interface VehicleTypesResponse {
   success: boolean;
   message: string;
   data: VehicleType[];
   timestamp: number;
}

export interface DriverVehicle {
   id: string;
   userProfileId: string;
   cityProductId: string | null;
   vehicleTypeId: string;
   vehicleNumber: string;
   isNocRequired: boolean;
   isPrimary: boolean;
   status: 'pending' | 'active' | 'inactive';
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
   vehicleType: VehicleType;
   cityProduct: any | null;
   _count: {
      driverVehicleDocuments: number;
   };
}

export interface DriverVehiclesResponse {
   success: boolean;
   message: string;
   data: DriverVehicle[];
   timestamp: number;
}

export interface CreateDriverVehicleRequest {
   profileId: string;
   vehicleTypeId: string;
   vehicleNumber: string;
   isPrimary: boolean;
}

export interface CreateDriverVehicleResponse {
   success: boolean;
   message: string;
   data: DriverVehicle;
   timestamp: number;
}

export interface VehicleVerificationResponse {
   success: boolean;
   message: string;
   data: {
      isNocRequired: boolean;
      id: string;
      driverVehicleId: string;
      vehicleDocumentId: string;
      documentUrl: string | null;
      documentFields: any | null;
      status: 'PENDING' | 'APPROVED' | 'REJECTED';
      details: {
         type: string;
         class: string;
         model: string;
         owner: string;
         engine: string;
         reg_no: string;
         status: string;
         chassis: string;
         reg_date: string;
         body_type: string;
         pucc_upto: string | null;
         rc_status: string;
         wheelbase: string;
         non_use_to: string | null;
         norms_type: string;
         noc_details: any | null;
         owner_count: string;
         permit_type: string | null;
         pucc_number: string | null;
         rc_financer: string | null;
         non_use_from: string | null;
         reference_id: number;
         status_as_on: string;
         is_commercial: boolean;
         mobile_number: string | null;
         permit_number: string | null;
         reg_authority: string;
         non_use_status: string | null;
         rc_expiry_date: string;
         unladen_weight: string;
         vehicle_colour: string;
         vehicle_number: string;
         challan_details: any[];
         present_address: string;
         rc_standard_cap: string;
         verification_id: string;
         blacklist_status: string;
         vehicle_category: string;
         vehicle_tax_upto: string;
         blacklist_details: any[];
         owner_father_name: string | null;
         permanent_address: string;
         permit_issue_date: string;
         permit_valid_from: string;
         permit_valid_upto: string;
         gross_vehicle_weight: string;
         national_permit_upto: string | null;
         vehicle_cylinders_no: string;
         split_present_address: {
            city: string[];
            state: string[][];
            country: string[];
            pincode: string;
            district: string[];
            address_line: string;
         };
         vehicle_seat_capacity: string;
         national_permit_number: string | null;
         vehicle_cubic_capacity: string;
         vehicle_insurance_upto: string;
         split_permanent_address: {
            city: string[];
            state: string[][];
            country: string[];
            pincode: string;
            district: string[];
            address_line: string;
         };
         vehicle_sleeper_capacity: string;
         national_permit_issued_by: string | null;
         vehicle_manufacturer_name: string;
         vehicle_standing_capacity: string;
         vehicle_insurance_company_name: string;
         vehicle_insurance_policy_number: string;
         vehicle_manufacturing_month_year: string;
      };
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
   };
   timestamp: number;
}

export interface UploadNocRequest {
   documentUrl: string;
}

// Vehicle Document Types
export interface VehicleDocument {
   id: string;
   name: string;
   identifier: string;
   countryId: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
   driverDocument: DriverVehicleDocument | null;
}

export interface DriverVehicleDocument {
   id: string;
   driverVehicleId: string;
   vehicleDocumentId: string;
   documentUrl: string | null;
   documentFields: Record<string, any> | null;
   status: 'PENDING' | 'APPROVED' | 'REJECTED';
   rejectionNote: string | null;
   details: Record<string, any> | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

export interface VehicleDocumentsResponse {
   success: boolean;
   message: string;
   data: VehicleDocument[];
   timestamp: number;
}

export interface ApproveRejectVehicleDocumentRequest {
   action: 'APPROVE' | 'REJECT';
   rejectionNote?: string;
}

export interface ChangeVehicleStatusRequest {
   status: 'active' | 'inactive';
}

export interface UploadVehicleDocumentRequest {
   documentUrl: string;
}

export interface UpdateDriverVehicleRequest {
   vehicleTypeId?: string;
   vehicleNumber?: string;
   isPrimary?: boolean;
}

export interface UpdateDriverStatusRequest {
   status: 'active' | 'inactive';
}
