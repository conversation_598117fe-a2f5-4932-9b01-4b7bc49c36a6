import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export interface FileUploadResponse {
   success: boolean;
   message: string;
   data: {
      key: string;
      url: string;
      contentType: string;
      size: number;
   };
}

/**
 * Global hook for uploading files to S3
 * Reusable across all modules that need file upload functionality
 */
export const useFileUpload = () => {
   return useMutation({
      mutationFn: async (file: File): Promise<FileUploadResponse> => {
         const formData = new FormData();
         formData.append('file', file);
         return apiClient.post('/file-upload/upload', formData, {
            headers: {
               'Content-Type': 'multipart/form-data',
            },
         });
      },
   });
};

/**
 * Global hook for deleting files from S3
 * Reusable across all modules that need file deletion functionality
 */
export const useFileDelete = () => {
   return useMutation({
      mutationFn: async (key: string): Promise<{ success: boolean; message: string }> => {
         return apiClient.delete('/file-upload/delete', {
            data: { key },
         });
      },
   });
};