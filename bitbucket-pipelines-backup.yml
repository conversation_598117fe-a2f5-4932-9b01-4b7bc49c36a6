image: atlassian/default-image:4

definitions:
  caches:
    pnpm: ~/.local/share/pnpm/store

pipelines:
  branches:
    staging-server:
      - parallel:
          - step:
              name: Build and Push API to ECR (Staging)
              services:
                - docker
              caches:
                - docker
                - pnpm
              script:
                - set -e  # Exit on any error
                - echo "🚀 Building and pushing API service for STAGING environment"
                
                # Install and configure AWS CLI
                - echo "Installing AWS CLI..."
                - apt-get update && apt-get install -y python3-pip curl unzip || { echo "Failed to install dependencies"; exit 1; }
                - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }
                - aws --version
                
                # Verify required environment variables
                - |
                  if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                    echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                    exit 1
                  fi
                
                - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
                - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
                - aws configure set default.region ap-south-1
                
                # Test AWS connectivity
                - echo "Testing AWS connectivity..."
                - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }
                
                # Login to ECR
                - echo "Logging into ECR..."
                - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 111311033809.dkr.ecr.ap-south-1.amazonaws.com || { echo "Failed to login to ECR"; exit 1; }
                
                # Build and tag API image
                - echo "Building API Docker image for staging..."
                - docker build -f docker/Dockerfile.api -t api:$BITBUCKET_BUILD_NUMBER . || { echo "Failed to build API image"; exit 1; }
                - docker tag api:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:$BITBUCKET_BUILD_NUMBER
                - docker tag api:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:latest
                - docker tag api:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:staging
                
                # Push API image
                - echo "Pushing API image to ECR..."
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:$BITBUCKET_BUILD_NUMBER || { echo "Failed to push API image with build number"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:latest || { echo "Failed to push API image with latest tag"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:staging || { echo "Failed to push API image with staging tag"; exit 1; }
                
                - echo "✅ API service pushed successfully to STAGING"

          - step:
              name: Build and Push Kafka Consumer to ECR (Staging)
              services:
                - docker
              caches:
                - docker
                - pnpm
              script:
                - set -e  # Exit on any error
                - echo "🚀 Building and pushing Kafka Consumer service for STAGING environment"
                
                # Install and configure AWS CLI
                - echo "Installing AWS CLI..."
                - apt-get update && apt-get install -y python3-pip curl unzip || { echo "Failed to install dependencies"; exit 1; }
                - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }
                - aws --version
                
                # Verify required environment variables
                - |
                  if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                    echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                    exit 1
                  fi
                
                - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
                - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
                - aws configure set default.region ap-south-1
                
                # Test AWS connectivity
                - echo "Testing AWS connectivity..."
                - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }
                
                # Login to ECR
                - echo "Logging into ECR..."
                - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 111311033809.dkr.ecr.ap-south-1.amazonaws.com || { echo "Failed to login to ECR"; exit 1; }
                
                # Build and tag Kafka Consumer image
                - echo "Building Kafka Consumer Docker image for staging..."
                - docker build -f docker/Dockerfile.kafka-consumer -t kafka-consumer:$BITBUCKET_BUILD_NUMBER . || { echo "Failed to build Kafka Consumer image"; exit 1; }
                - docker tag kafka-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:$BITBUCKET_BUILD_NUMBER
                - docker tag kafka-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:latest
                - docker tag kafka-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:staging
                
                # Push Kafka Consumer image
                - echo "Pushing Kafka Consumer image to ECR..."
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:$BITBUCKET_BUILD_NUMBER || { echo "Failed to push Kafka Consumer image with build number"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:latest || { echo "Failed to push Kafka Consumer image with latest tag"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:staging || { echo "Failed to push Kafka Consumer image with staging tag"; exit 1; }
                
                - echo "✅ Kafka Consumer service pushed successfully to STAGING"

          - step:
              name: Build and Push RabbitMQ Consumer to ECR (Staging)
              services:
                - docker
              caches:
                - docker
                - pnpm
              script:
                - set -e  # Exit on any error
                - echo "🚀 Building and pushing RabbitMQ Consumer service for STAGING environment"
                
                # Install and configure AWS CLI
                - echo "Installing AWS CLI..."
                - apt-get update && apt-get install -y python3-pip curl unzip || { echo "Failed to install dependencies"; exit 1; }
                - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }
                - aws --version
                
                # Verify required environment variables
                - |
                  if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                    echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                    exit 1
                  fi
                
                - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
                - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
                - aws configure set default.region ap-south-1
                
                # Test AWS connectivity
                - echo "Testing AWS connectivity..."
                - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }
                
                # Login to ECR
                - echo "Logging into ECR..."
                - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 111311033809.dkr.ecr.ap-south-1.amazonaws.com || { echo "Failed to login to ECR"; exit 1; }
                
                # Build and tag RabbitMQ Consumer image
                - echo "Building RabbitMQ Consumer Docker image for staging..."
                - docker build -f docker/Dockerfile.rabbitmq-consumer -t rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER . || { echo "Failed to build RabbitMQ Consumer image"; exit 1; }
                - docker tag rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER
                - docker tag rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:latest
                - docker tag rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:staging
                
                # Push RabbitMQ Consumer image
                - echo "Pushing RabbitMQ Consumer image to ECR..."
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER || { echo "Failed to push RabbitMQ Consumer image with build number"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:latest || { echo "Failed to push RabbitMQ Consumer image with latest tag"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:staging || { echo "Failed to push RabbitMQ Consumer image with staging tag"; exit 1; }
                
                - echo "✅ RabbitMQ Consumer service pushed successfully to STAGING"
